<scroll-view style="{{'height:'+(scrollH+'px')+';'}}" scroll-y="true" data-event-opts="{{[['scrolltolower',[['lower',['$event']]]]]}}" bindscrolltolower="__e"><view class="main recent_orders"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="box order_lists"><view class="date_type"><text class="time">{{item.$orig.orderTime+" "+item.$orig.id}}</text><text class="{{['type','status',(item.$orig.status==2)?'status':'']}}">{{item.m0}}</text></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" class="orderBox" bindtap="__e"><view class="food_num"><scroll-view class="pic" style="width:100%;overflow:hidden;white-space:nowrap;" scroll-x="true"><block wx:for="{{item.$orig.orderDetailList}}" wx:for-item="num" wx:for-index="y" wx:key="y"><view class="food_num_item"><view class="img"><image src="{{num.image}}"></image></view><view class="food">{{num.name}}</view></view></block></scroll-view></view><view class="numAndAum"><view><text>{{"￥"+item.g0}}</text></view><view><text>{{"共"+item.m1.count+"件"}}</text></view></view></view><view class="againBtn"><button class="new_btn" type="default" data-event-opts="{{[['tap',[['oneOrderFun',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">再来一单</button><block wx:if="{{item.m2}}"><button class="new_btn btn" type="default" data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">去支付</button></block></view></view></block></view><block wx:if="{{loading}}"><reach-bottom vue-id="5de6238e-1" loadingText="{{loadingText}}" bind:__l="__l"></reach-bottom></block></scroll-view>