{"version": 3, "sources": ["webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/order/components/dishInfo.vue?4a14", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/order/components/dishInfo.vue?86bb", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/order/components/dishInfo.vue?70dd", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/order/components/dishInfo.vue?7183", "uni-app:///pages/order/components/dishInfo.vue"], "names": ["props", "remark", "type", "default", "tablewareData", "radioGroup", "activeRadio", "baseData", "components", "Pikers", "methods", "goRemark", "openPopuos", "change", "closePopup", "handlePiker", "changeCont", "handleRadio"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACuC;;;AAG9F;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAgyB,CAAgB,kxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkFpzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;QAAA;MAAA;IACA;EACA;EACAK;IAAAC;EAAA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B", "file": "pages/order/components/dishInfo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dishInfo.vue?vue&type=template&id=cc9ab452&scoped=true&\"\nvar renderjs\nimport script from \"./dishInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./dishInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./../style.scss?vue&type=style&index=0&id=cc9ab452&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cc9ab452\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/components/dishInfo.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dishInfo.vue?vue&type=template&id=cc9ab452&scoped=true&\"", "var components\ntry {\n  components = {\n    uniList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list/uni-list\" */ \"@/uni_modules/uni-list/components/uni-list/uni-list.vue\"\n      )\n    },\n    uniListItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list-item/uni-list-item\" */ \"@/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dishInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dishInfo.vue?vue&type=script&lang=js&\"", "<!--备注、餐具、发票弹-->\n<template>\n  <view class=\"box order_list\">\n    <!-- 备注、餐具、发票 -->\n    <view class=\"uniInfo\">\n      <!-- 备注 -->\n      <view @click=\"goRemark\">\n        <uni-list>\n          <uni-list-item showArrow title=\"备注\" class=\"uniListItem\">\n            <template v-slot:footer>\n              <text class=\"temarkText\">{{\n                remark ? remark : \"推荐使用无接触配送\"\n              }}</text>\n            </template>\n          </uni-list-item>\n        </uni-list>\n      </view>\n      <!-- end -->\n      <!-- 餐具数量 -->\n      <view @click=\"openPopuos('bottom')\">\n        <uni-list>\n          <uni-list-item showArrow title=\"餐具数量\" class=\"uniListItem\">\n            <template v-slot:footer>\n              <text>已在店选择：{{ tablewareData }}</text>\n            </template>\n          </uni-list-item>\n        </uni-list>\n      </view>\n      <!-- end -->\n      <!-- 发票 -->\n      <view class=\"invoiceBox\">\n        <uni-list>\n          <uni-list-item title=\"发票\" class=\"uniListItem\">\n            <template v-slot:footer>\n              <text>请联系商家提供</text>\n            </template>\n          </uni-list-item>\n        </uni-list>\n      </view>\n      <!-- end -->\n      <view class=\"container\">\n        <!-- 餐具弹层 -->\n        <uni-popup ref=\"popup\" @change=\"change\" class=\"popupBox\">\n          <view class=\"popup-content\">\n            <view class=\"popupTitle\">\n              <text>按政府条例要求： </text>\n              <text>商家不得主动向您提供一次性餐具，请按需选择餐具数量</text>\n            </view>\n            <view class=\"popupCon\">\n              <view class=\"popupBtn\">\n                <text @click=\"closePopup\">取消</text>\n                <text>选择本单餐具</text>\n                <text @click=\"handlePiker\">确定</text>\n              </view>\n              <pikers\n                :baseData=\"baseData\"\n                ref=\"piker\"\n                @changeCont=\"changeCont\"\n              ></pikers>\n            </view>\n            <view class=\"popupSet\">\n              <view>后续订单餐具设置</view>\n              <view>\n                <radio-group @change=\"handleRadio\">\n                  <label v-for=\"item in radioGroup\" :key=\"item\">\n                    <radio\n                      :value=\"item\"\n                      color=\"#FFC200\"\n                      :checked=\"item == activeRadio\"\n                    />{{ item }}\n                  </label>\n                </radio-group>\n              </view>\n            </view>\n          </view>\n        </uni-popup>\n        <!-- end -->\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nimport { editHoppingCart } from '../../api/api'\nimport Pikers from '@/components/uni-piker/index.vue'\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 进入备注页\n    remark: {\n      type: String,\n      default: '',\n    },\n    // 选择的餐具信息\n    tablewareData: {\n      type: String,\n      default: '',\n    },\n    // 后续订单餐具设置\n    radioGroup: {\n      type: Array,\n      default: () => [],\n    },\n    // 当前选择的\n    activeRadio: {\n      type: String,\n      default: '',\n    },\n    // 本单餐具数据\n    baseData: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  components: { Pikers },\n  methods: {\n    // 进入备注页面\n    goRemark () {\n      this.$emit(\"goRemark\")\n    },\n    // 打开餐具数量弹出层\n    openPopuos (type) {\n      this.$refs.popup.open(type)\n    },\n    change () {\n      this.$emit(\"change\")\n    },\n    // 取消本单餐具\n    closePopup (type) {\n      this.$refs.popup.close(type)\n    },\n    // 确定本单餐具\n    handlePiker () {\n      this.$emit('handlePiker')\n      this.closePopup()\n    },\n    // 触发本单餐具\n    changeCont (val) {\n      this.$emit(\"changeCont\", val)\n    },\n    // 餐具数量的后续订单餐具设置\n    handleRadio (e) {\n      this.$emit(\"handleRadio\", e)\n    },\n  },\n};\n</script>\n<style src=\"./../style.scss\" lang=\"scss\" scoped></style>"], "sourceRoot": ""}