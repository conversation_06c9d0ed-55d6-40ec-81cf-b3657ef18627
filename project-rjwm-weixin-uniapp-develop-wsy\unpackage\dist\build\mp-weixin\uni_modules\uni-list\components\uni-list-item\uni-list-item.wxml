<view class="{{['uni-list-item',(disabled)?'uni-list-item--disabled':'']}}" style="{{'background-color:'+(customStyle.backgroundColor)+';'}}" hover-class="{{!clickable&&!link||disabled||showSwitch?'':'uni-list-item--hover'}}" data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" bindtap="__e"><block wx:if="{{!isFirstChild}}"><view class="{{['border--left',(border)?'uni-list--border':'']}}"></view></block><view class="{{['uni-list-item__container',(showArrow||link)?'container--right':'',(direction==='column')?'flex--direction':'']}}" style="{{'padding-top:'+(padding.top)+';'+('padding-left:'+(padding.left)+';')+('padding-right:'+(padding.right)+';')+('padding-bottom:'+(padding.bottom)+';')}}"><block wx:if="{{$slots.header}}"><slot name="header"></slot></block><block wx:else><view class="uni-list-item__header"><block wx:if="{{thumb}}"><view class="uni-list-item__icon"><image class="{{['uni-list-item__icon-img','uni-list--'+thumbSize]}}" src="{{thumb}}"></image></view></block><block wx:else><block wx:if="{{showExtraIcon}}"><view class="uni-list-item__icon"><uni-icons vue-id="7e11c7e5-1" customPrefix="{{extraIcon.customPrefix}}" color="{{extraIcon.color}}" size="{{extraIcon.size}}" type="{{extraIcon.type}}" bind:__l="__l"></uni-icons></view></block></block></view></block><block wx:if="{{$slots.body}}"><slot name="body"></slot></block><block wx:else><view class="{{['uni-list-item__content',(thumb||showExtraIcon||showBadge||showSwitch)?'uni-list-item__content--center':'']}}"><block wx:if="{{title}}"><text class="{{['uni-list-item__content-title',ellipsis!==0&&ellipsis<=2?'uni-ellipsis-'+ellipsis:'']}}">{{title}}</text></block><block wx:if="{{note}}"><text class="uni-list-item__content-note">{{note}}</text></block></view></block><block wx:if="{{$slots.footer}}"><slot name="footer"></slot></block><block wx:else><block wx:if="{{rightText||showBadge||showSwitch}}"><view class="{{['uni-list-item__extra',(direction==='column')?'flex--justify':'']}}"><block wx:if="{{rightText}}"><text class="uni-list-item__extra-text">{{rightText}}</text></block><block wx:if="{{showBadge}}"><uni-badge vue-id="7e11c7e5-2" type="{{badgeType}}" text="{{badgeText}}" custom-style="{{badgeStyle}}" bind:__l="__l"></uni-badge></block><block wx:if="{{showSwitch}}"><switch disabled="{{disabled}}" checked="{{switchChecked}}" data-event-opts="{{[['change',[['onSwitchChange',['$event']]]]]}}" bindchange="__e"></switch></block></view></block></block></view><block wx:if="{{showArrow||link}}"><uni-icons class="uni-icon-wrapper" vue-id="7e11c7e5-3" size="{{16}}" color="#bbb" type="arrowright" bind:__l="__l"></uni-icons></block></view>