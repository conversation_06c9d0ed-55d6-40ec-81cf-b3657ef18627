(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/my/my"],{2112:function(e,t,n){"use strict";n.r(t);var r=n("8dad"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},4984:function(e,t,n){"use strict";var r=n("7f11"),a=n.n(r);a.a},"7f11":function(e,t,n){},"8dad":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("7eb4")),o=r(n("ee10")),s=r(n("7ca3")),i=n("b370"),c=n("8f59"),u=n("b99c");function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={data:function(){return{psersonUrl:"../../static/btn_waiter_sel.png",nickName:"",gender:"0",phoneNumber:"18500557668",recentOrdersList:[],sumOrder:{amount:0,number:0},status:"",scrollH:0,pageInfo:{page:1,pageSize:10,total:0},loadingText:"",loading:!1}},components:{HeadInfo:function(){n.e("pages/my/components/headInfo").then(function(){return resolve(n("f346"))}.bind(null,n)).catch(n.oe)},OrderInfo:function(){n.e("pages/my/components/orderInfo").then(function(){return resolve(n("b0df"))}.bind(null,n)).catch(n.oe)},OrderList:function(){n.e("pages/my/components/orderList").then(function(){return resolve(n("e28d"))}.bind(null,n)).catch(n.oe)}},filters:{getPhoneNum:function(e){return e.replace(/\-/g,"")}},onLoad:function(){this.psersonUrl=this.$store.state.baseUserInfo&&this.$store.state.baseUserInfo.avatarUrl,this.nickName=this.$store.state.baseUserInfo&&this.$store.state.baseUserInfo.nickName,this.gender=this.$store.state.baseUserInfo&&this.$store.state.baseUserInfo.gender,this.getList()},created:function(){},onReady:function(){var t=this;e.getSystemInfo({success:function(n){t.scrollH=n.windowHeight-e.upx2px(100)}})},methods:f(f({},(0,c.mapMutations)(["setAddressBackUrl"])),{},{statusWord:function(e){return(0,u.statusWord)(e.status,e.time)},getOvertime:function(e){return(0,u.getOvertime)(e)},getList:function(){var e=this,t={pageSize:10,page:this.pageInfo.page};(0,i.getOrderPage)(t).then((function(t){1===t.code&&(e.recentOrdersList=e.recentOrdersList.concat(t.data.records),e.pageInfo.total=t.data.total,e.loadingText="",e.loading=!1)}))},goAddress:function(){this.setAddressBackUrl("/pages/my/my"),e.redirectTo({url:"/pages/address/address?form=my"})},goOrder:function(){e.navigateTo({url:"/pages/historyOrder/historyOrder"})},oneOrderFun:function(t){return(0,o.default)(a.default.mark((function n(){var r,o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=getCurrentPages(),o=r.findIndex((function(e){return"pages/index/index"===e.route})),n.next=4,(0,i.delShoppingCart)();case 4:(0,i.repetitionOrder)(t).then((function(t){1===t.code&&e.navigateBack({delta:o>-1?r.length-o:1})}));case 5:case"end":return n.stop()}}),n)})))()},quitClick:function(){},goDetail:function(t){this.setAddressBackUrl("/pages/my/my"),e.redirectTo({url:"/pages/details/index?orderId="+t})},dataAdd:function(){var e=Math.ceil(this.pageInfo.total/10);this.pageInfo.page===e?(this.loadingText="没有更多了",this.loading=!0):(this.pageInfo.page++,this.getList())},lower:function(){this.loadingText="数据加载中...",this.loading=!0,this.dataAdd()},goBack:function(){e.redirectTo({url:"/pages/index/index"})}})};t.default=l}).call(this,n("df3c")["default"])},"984b":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return r}));var r={uniNavBar:function(){return n.e("components/uni-nav-bar/uni-nav-bar").then(n.bind(null,"c455"))}},a=function(){var e=this.$createElement,t=(this._self._c,this.recentOrdersList&&this.recentOrdersList.length>0);this.$mp.data=Object.assign({},{$root:{g0:t}})},o=[]},a24a:function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("6134");r(n("3240"));var a=r(n("d5fb"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},d5fb:function(e,t,n){"use strict";n.r(t);var r=n("984b"),a=n("2112");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("4984");var s=n("828b"),i=Object(s["a"])(a["default"],r["b"],r["c"],!1,null,"53fd4916",null,!1,r["a"],void 0);t["default"]=i.exports}},[["a24a","common/runtime","common/vendor"]]]);