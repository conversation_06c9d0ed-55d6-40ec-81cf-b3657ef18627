<view class="customer-box data-v-7b2a13f8"><view class="wrap data-v-7b2a13f8"><view class="contion data-v-7b2a13f8"><view class="orderPay data-v-7b2a13f8"><view class="data-v-7b2a13f8"><block wx:if="{{timeout}}"><view class="data-v-7b2a13f8">订单已超时</view></block><block wx:else><view class="data-v-7b2a13f8">支付剩余时间<text class="data-v-7b2a13f8">{{rocallTime}}</text></view></block></view><view class="money data-v-7b2a13f8">￥<text class="data-v-7b2a13f8">{{orderDataInfo.orderAmount}}</text></view><view class="data-v-7b2a13f8">{{$root.m0.shopName+"-"+orderDataInfo.orderNumber}}</view></view></view><view class="box payBox data-v-7b2a13f8"><view class="contion data-v-7b2a13f8"><view class="example-body data-v-7b2a13f8"><radio-group data-event-opts="{{[['change',[['styleChange',['$event']]]]]}}" class="uni-list data-v-7b2a13f8" bindchange="__e"><view class="uni-list-item data-v-7b2a13f8"><block wx:for="{{payMethodList}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><view class="uni-list-item__container data-v-7b2a13f8"><view class="uni-list-item__content data-v-7b2a13f8"><icon class="wechatIcon data-v-7b2a13f8"></icon><text class="uni-list-item__content-title data-v-7b2a13f8">{{item}}</text></view><view class="uni-list-item__extra data-v-7b2a13f8"><radio class="radioIcon data-v-7b2a13f8" value="{{item}}" color="#FFC200" checked="{{index==activeRadio}}"></radio></view></view></block></view></radio-group></view></view></view><view class="bottomBox btnBox data-v-7b2a13f8"><button class="add_btn data-v-7b2a13f8" type="primary" plain="true" data-event-opts="{{[['tap',[['handleSave',['$event']]]]]}}" bindtap="__e">确认支付</button></view></view></view>