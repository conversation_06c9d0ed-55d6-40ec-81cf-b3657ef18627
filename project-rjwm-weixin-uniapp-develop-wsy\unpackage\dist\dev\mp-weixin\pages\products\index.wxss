@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.products-list-container.data-v-fb16ccc0 {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.navbar.data-v-fb16ccc0 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.navbar .nav-back.data-v-fb16ccc0 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar .nav-back .back-icon.data-v-fb16ccc0 {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
}
.navbar .nav-title.data-v-fb16ccc0 {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-right: 60rpx;
}
.products-grid.data-v-fb16ccc0 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 30rpx;
}
.product-card.data-v-fb16ccc0 {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.product-card.data-v-fb16ccc0:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.product-image-wrapper.data-v-fb16ccc0 {
  width: 100%;
  height: 200rpx;
  overflow: hidden;
}
.product-image-wrapper .product-image.data-v-fb16ccc0 {
  width: 100%;
  height: 100%;
}
.product-info.data-v-fb16ccc0 {
  padding: 20rpx;
}
.product-info .product-name.data-v-fb16ccc0 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.product-info .product-price.data-v-fb16ccc0 {
  display: flex;
  align-items: baseline;
}
.product-info .product-price .price-symbol.data-v-fb16ccc0 {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: bold;
}
.product-info .product-price .price-value.data-v-fb16ccc0 {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-left: 5rpx;
}
.product-button.data-v-fb16ccc0 {
  padding: 0 20rpx 20rpx;
}
.product-button .view-button.data-v-fb16ccc0 {
  width: 100%;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: #fff;
  font-size: 24rpx;
  border-radius: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

