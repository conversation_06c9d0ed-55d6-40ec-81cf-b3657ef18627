{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/addOrEditAddress/addOrEditAddress.vue?b34f", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/addOrEditAddress/addOrEditAddress.vue?7aa7", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/addOrEditAddress/addOrEditAddress.vue?e2a9", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/addOrEditAddress/addOrEditAddress.vue?95e0", "uni-app:///pages/addOrEditAddress/addOrEditAddress.vue", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/addOrEditAddress/addOrEditAddress.vue?4023", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/addOrEditAddress/addOrEditAddress.vue?be3c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "simpleAddress", "data", "platform", "showDel", "showInput", "valueMan", "valueWoman", "showClass", "items", "value", "name", "current", "options", "type", "form", "phone", "sex", "provinceCode", "provinceName", "cityCode", "cityName", "districtCode", "districtName", "detail", "cityPickerValueDefault", "pickerText", "address", "delId", "onLoad", "uni", "title", "onUnload", "key", "computed", "statusBarHeight", "created", "methods", "init", "goBack", "url", "queryAddressBookById", "id", "res", "isClass", "openAddres", "onConfirm", "bindTextAreaBlur", "radioChange", "sex<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addAddressFun", "duration", "icon", "label", "consignee", "deleteAddressFun", "getTextOption"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAwyB,CAAgB,0xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACmI5zB;AAKA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,QACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC,UACA;QACAF;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,EACA;MACA;MACAC;QACAJ;QACAK;QACAF;QACAG;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;MACAC;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAF;MACAG;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACAT;QACAU;MACA;IACA;IACA;IACAC;MAAA;MACA;QAAAC;MAAA;QACA;UACA;YACAxB;YACAE;YACAE;YACAN;YACAL;YACAM;YACAH;YACAU;YACAkB;UACA;UACA,IACAC,yBACAA,qBACAA,uBACA;YACA,gBACAA,wBACA,MACAA,oBACA,MACAA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MAEAf;IACA;IACAgB;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,gDACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;UACAnB;UACAoB;UACAC;QACA;MACA;QACA;UACArB;UACAoB;UACAC;QACA;MACA;QACA;UACArB;UACAoB;UACAC;QACA;MACA;QACA;UACArB;UACAoB;UACAC;QACA;MACA;QACA;UACArB;UACAoB;UACAC;QACA;MACA;MAEA;QACA,UACA;QACA;UACA;YACArB;YACAoB;YACAC;UACA;QACA;MACA;MACA;QACA;QACA;UACA;YACArB;YACAoB;YACAC;UACA;QACA;MACA;MACA,6CACA;QACAC;QACAC;QACAnC;QACAE;QACAE;MAAA,EACA;MACA;MACA;QACA;UACA;YACAO;cACAU;YACA;UACA;QACA;MACA;QACA;QACA;UACA;YACAV;cACAU;YACA;UACA;QACA;MACA;IACA;IACA;IACAe;MAAA;MACA;QACA;UACAzB;YACAU;UACA;UACAV;YACAC;YACAoB;YACAC;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpZA;AAAA;AAAA;AAAA;AAAmgD,CAAgB,i6CAAG,EAAC,C;;;;;;;;;;;ACAvhD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/addOrEditAddress/addOrEditAddress.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/addOrEditAddress/addOrEditAddress.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./addOrEditAddress.vue?vue&type=template&id=174d7646&scoped=true&\"\nvar renderjs\nimport script from \"./addOrEditAddress.vue?vue&type=script&lang=js&\"\nexport * from \"./addOrEditAddress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./addOrEditAddress.vue?vue&type=style&index=0&id=174d7646&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"174d7646\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/addOrEditAddress/addOrEditAddress.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addOrEditAddress.vue?vue&type=template&id=174d7646&scoped=true&\"", "var components\ntry {\n  components = {\n    uniNavBar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-bar/uni-nav-bar\" */ \"@/components/uni-nav-bar/uni-nav-bar.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addOrEditAddress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addOrEditAddress.vue?vue&type=script&lang=js&\"", "<!--新增收获地址-->\n<template>\n  <view class=\"customer-box\">\n    <uni-nav-bar\n      @clickLeft=\"goBack\"\n      left-icon=\"back\"\n      leftIcon=\"arrowleft\"\n      :title=\"delId ? '编辑收货地址' : '新增收货地址'\"\n      statusBar=\"true\"\n      fixed=\"true\"\n      color=\"#ffffff\"\n      backgroundColor=\"#333333\"\n    ></uni-nav-bar>\n    <view\n      class=\"add_edit\"\n      :style=\"{ height: `calc(100% - ${statusBarHeight} - 44px)` }\"\n    >\n      <form class=\"form_address\">\n        <view class=\"uni-form-item uni-column form_item\">\n          <view class=\"title\">联系人:</view>\n          <uni-easyinput\n            class=\"uni-input\"\n            v-model=\"form.name\"\n            placeholder-class=\"uni-place\"\n            placeholder=\"请填写收货人的姓名\"\n            minlength=\"2\"\n            maxlength=\"12\"\n          />\n          <view class=\"radio\">\n            <view\n              class=\"radio-item\"\n              v-for=\"(item, index) in items\"\n              :key=\"item.value\"\n              @click=\"sexChangeHandle(item.value)\"\n            >\n              <image\n                v-if=\"item.value != form.sex\"\n                class=\"radio-img\"\n                src=\"../../static/icon-radio.png\"\n              ></image>\n              <image\n                v-else\n                class=\"radio-img\"\n                src=\"../../static/icon-radio-selected.png\"\n              ></image>\n              <text class=\"radio-label\">{{ item.name }}</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"uni-form-item uni-column form_item\">\n          <view class=\"title\">手机号:</view>\n          <uni-easyinput\n            class=\"uni-input\"\n            v-model=\"form.phone\"\n            type=\"number\"\n            placeholder-class=\"uni-place\"\n            placeholder=\"请填写收货人手机号码\"\n            maxlength=\"11\"\n          />\n        </view>\n        <view class=\"uni-form-item uni-column form_item pad\">\n          <view class=\"title\">收货地址:</view>\n          <!-- 联动省市县 -->\n          <view class=\"update-input\">\n            <view class=\"update-adress\" @click=\"openAddres\">\n              <text\n                v-if=\"showInput\"\n                class=\"uni-input\"\n                :class=\"address !== '' ? '' : 'uni-place'\"\n                >{{ address !== \"\" ? address : \"省/市/区\" }}</text\n              >\n              <text class=\"addressIcon\">\n                <text class=\"icon\" :class=\"showClass ? 'iconOn' : ''\"></text>\n              </text>\n            </view>\n\n            <!-- 详细地址 -->\n            <textarea\n              class=\"detail\"\n              :class=\"{ 'detail-ios': platform == 'ios' }\"\n              placeholder-class=\"uni-place\"\n              v-model=\"form.detail\"\n              placeholder=\"详细地址（精确到门牌号）\"\n            ></textarea>\n          </view>\n        </view>\n        <view class=\"uni-form-item uni-column form_item tag-box\">\n          <view class=\"title\">标签:</view>\n          <text\n            :class=\"{ active: form.type === item.type }\"\n            class=\"tag_text\"\n            v-for=\"item in options\"\n            :key=\"item.type\"\n            @click=\"getTextOption(item)\"\n            >{{ item.name }}</text\n          >\n        </view>\n      </form>\n\n      <view class=\"add_address\">\n        <button\n          class=\"add_btn\"\n          type=\"primary\"\n          plain=\"true\"\n          @click=\"addAddressFun()\"\n        >\n          保存地址\n        </button>\n        <button\n          v-if=\"showDel\"\n          class=\"del_btn\"\n          type=\"default\"\n          plain=\"true\"\n          @click=\"deleteAddressFun()\"\n        >\n          删除地址\n        </button>\n      </view>\n    </view>\n    <simple-address\n      ref=\"simpleAddress\"\n      :pickerValueDefault=\"cityPickerValueDefault\"\n      @onConfirm=\"onConfirm\"\n      @isClass=\"isClass\"\n      themeColor=\"#F58C21\"\n    ></simple-address>\n  </view>\n</template>\n\n<script>\nimport simpleAddress from \"../common/simple-address/simple-address.nvue\"\nimport {\n  addAddressBook,\n  delAddressBook,\n  queryAddressBookById,\n  editAddressBook,\n} from \"../api/api.js\"\n\nexport default {\n  components: {\n    simpleAddress,\n  },\n  data () {\n    return {\n      platform: \"ios\",\n      showDel: false,\n      showInput: true,\n      valueMan: true,\n      valueWoman: true,\n      showClass: false,\n      items: [\n        {\n          value: \"0\",\n          name: \"先生\",\n        },\n        {\n          value: \"1\",\n          name: \"女士\",\n        },\n      ],\n      current: 0,\n      options: [\n        {\n          name: \"公司\",\n          type: 1,\n        },\n        {\n          name: \"家\",\n          type: 2,\n        },\n        {\n          name: \"学校\",\n          type: 3,\n        },\n      ],\n      // type: 1,\n      form: {\n        name: \"\",\n        phone: \"\",\n        type: 1,\n        sex: \"0\",\n        provinceCode: \"11\",\n        provinceName: \"\",\n        cityCode: \"1101\",\n        cityName: \"\",\n        districtCode: \"110102\",\n        districtName: \"\",\n        detail: \"\",\n      },\n      // 联动省市县\n      // 弹框的初始值\n      cityPickerValueDefault: [0, 0, 1],\n      pickerText: \"\",\n      // 初始值\n      address: \"\",\n      // 保存将要删除的\n      delId: \"\",\n    }\n  },\n  onLoad (options) {\n    this.init()\n    if (options && options.type === \"编辑\") {\n      this.delId = \"\"\n      this.showDel = true\n      uni.setNavigationBarTitle({\n        title: \"编辑收获地址\",\n      })\n      // 保存将要删除的id\n      this.delId = options.id\n      // 查询详情的接口\n      this.queryAddressBookById(options.id)\n    } else {\n      this.showDel = false\n    }\n  },\n  onUnload () {\n    uni.removeStorage({\n      key: \"edit\",\n    })\n  },\n  computed: {\n    statusBarHeight () {\n      return uni.getSystemInfoSync().statusBarHeight + \"px\"\n    },\n  },\n  created () { },\n  methods: {\n    init () {\n      const res = uni.getSystemInfoSync()\n      this.platform = res.platform\n    },\n    goBack () {\n      uni.redirectTo({\n        url: \"/pages/address/address\",\n      })\n    },\n    // 查询地址详情接口\n    queryAddressBookById (id) {\n      queryAddressBookById({ id }).then((res) => {\n        if (res.code === 1) {\n          this.form = {\n            provinceCode: res.data.provinceCode,\n            cityCode: res.data.cityCode,\n            districtCode: res.data.districtCode,\n            phone: res.data.phone,\n            name: res.data.consignee,\n            sex: res.data.sex,\n            type: Number(res.data.label),\n            detail: res.data.detail,\n            id: res.data.id,\n          }\n          if (\n            res.data.provinceName &&\n            res.data.cityName &&\n            res.data.districtName\n          ) {\n            this.address =\n              res.data.provinceName +\n              \"/\" +\n              res.data.cityName +\n              \"/\" +\n              res.data.districtName\n          }\n        }\n      })\n    },\n    isClass (val) {\n      this.showClass = val\n    },\n    openAddres () {\n      this.$refs.simpleAddress.open()\n\n      uni.hideKeyboard()\n    },\n    onConfirm (e) {\n      this.form.provinceCode = e.provinceCode\n      this.form.cityCode = e.cityCode\n      this.form.districtCode = e.areaCode\n      // 把选择的地址回显到input框中\n      this.address = e.label\n    },\n    bindTextAreaBlur: function (e) {\n    },\n    radioChange (e) {\n      if (e.detail.value === \"man\") {\n        this.form.radio = 0\n      } else {\n        this.form.radio = 1\n      }\n    },\n    sexChangeHandle (val) {\n      this.form.sex = val\n    },\n    // 保存地址\n    addAddressFun () {\n      if (this.form.name === \"\") {\n        return uni.showToast({\n          title: \"联系人不能为空\",\n          duration: 1000,\n          icon: \"none\",\n        })\n      } else if (this.form.phone === \"\") {\n        return uni.showToast({\n          title: \"手机号不能为空\",\n          duration: 1000,\n          icon: \"none\",\n        })\n      } else if (this.form.type === \"\") {\n        return uni.showToast({\n          title: \"所属标签不能为空\",\n          duration: 1000,\n          icon: \"none\",\n        })\n      } else if (this.address === \"\") {\n        return uni.showToast({\n          title: \"所在地区不能为空\",\n          duration: 1000,\n          icon: \"none\",\n        })\n      } else if (this.form.detail === \"\") {\n        return uni.showToast({\n          title: \"详细地址不能为空不能为空\",\n          duration: 1000,\n          icon: \"none\",\n        })\n      }\n\n      if (this.form.phone) {\n        const reg =\n          /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$/\n        if (!reg.test(this.form.phone)) {\n          return uni.showToast({\n            title: \"手机号输入有误\",\n            duration: 1000,\n            icon: \"none\",\n          })\n        }\n      }\n      if (this.form.name) {\n        const reg = /^[\\u0391-\\uFFE5A-Za-z0-9]{2,12}$/\n        if (!reg.test(this.form.name)) {\n          return uni.showToast({\n            title: \"请输入合法的2-12个字符\",\n            duration: 1000,\n            icon: \"none\",\n          })\n        }\n      }\n      const params = {\n        ...this.form,\n        label: this.form.type,\n        consignee: this.form.name,\n        provinceName: this.address.split(\"/\")[0],\n        cityName: this.address.split(\"/\")[1],\n        districtName: this.address.split(\"/\")[2],\n      }\n      // 编辑\n      if (this.showDel) {\n        editAddressBook(params).then((res) => {\n          if (res.code === 1) {\n            uni.redirectTo({\n              url: \"/pages/address/address\",\n            })\n          }\n        })\n      } else {\n        delete params.id\n        addAddressBook(params).then((res) => {\n          if (res.code === 1) {\n            uni.redirectTo({\n              url: \"/pages/address/address\",\n            })\n          }\n        })\n      }\n    },\n    // 删除地址\n    deleteAddressFun () {\n      delAddressBook(this.delId).then((res) => {\n        if (res.code === 1) {\n          uni.redirectTo({\n            url: \"/pages/address/address\",\n          })\n          uni.showToast({\n            title: \"地址删除成功\",\n            duration: 1000,\n            icon: \"none\",\n          })\n          this.form.name = \"\"\n          this.form.phone = \"\"\n          this.form.address = \"\"\n          this.form.type = 1\n          this.form.radio = 0\n          this.form.provinceCode = \"11\"\n          this.form.cityCode = \"1101\"\n          this.form.districtCode = \"110102\"\n        }\n      })\n    },\n    // 标签的事件\n    getTextOption (item) {\n      this.form.type = item.type\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.add_edit {\n  width: 750rpx;\n  height: 100%;\n  background-color: #fff;\n  .form_address {\n    .form_item {\n      margin: 0 22rpx;\n      padding: 36rpx 0;\n      border-bottom: 1px solid #efefef;\n      display: flex;\n      align-items: center;\n      &.pad {\n        padding-bottom: 10rpx;\n        align-items: baseline;\n      }\n      .title {\n        width: 140rpx;\n        opacity: 1;\n        font-size: 28rpx;\n        font-family: PingFangSC, PingFangSC-Medium;\n        font-weight: 600;\n        text-align: left;\n        color: #333333;\n        letter-spacing: 0px;\n        line-height: 44rpx;\n      }\n      /deep/ .is-input-border {\n        border: 0 none;\n        border-radius: none;\n        min-height: auto;\n        line-height: 16rpx;\n        .uni-easyinput__content-input {\n          padding-left: 0 !important;\n          font-size: 26rpx;\n        }\n        .uni-easyinput__placeholder-class {\n          font-size: 26rpx;\n        }\n        .uni-easyinput__content-textarea {\n          padding: 18rpx 0 0;\n          width: 100%;\n          min-height: 60rpx;\n          box-sizing: border-box;\n          overflow: visible;\n          height: auto;\n          font-size: 26rpx;\n        }\n      }\n      .uni-input {\n        flex: 1;\n      }\n      /deep/ .uni-place {\n        font-size: 26rpx;\n        font-family: PingFangSC, PingFangSC-Regular;\n        font-weight: 400;\n        color: #999999 !important;\n      }\n      .radio {\n        opacity: 1;\n        font-size: 26rpx;\n        font-family: PingFangSC, PingFangSC-Regular;\n        font-weight: 400;\n        text-align: left;\n        color: #333333;\n        letter-spacing: 0px;\n        height: 40rpx;\n        display: flex;\n        padding-right: 20rpx;\n        margin-left: 20rpx;\n        .radio-item {\n          display: flex;\n          align-items: center;\n          &:first-child {\n            margin-right: 54rpx;\n          }\n        }\n        .radio-img {\n          width: 32rpx;\n          height: 32rpx;\n          margin-right: 10rpx;\n        }\n      }\n\n      // 标签\n      .tag_text {\n        width: 68rpx;\n        height: 44rpx;\n        line-height: 40rpx;\n        border: 1px solid #e5e4e4;\n        display: inline-block;\n        border-radius: 6rpx;\n        text-align: center;\n        box-sizing: border-box;\n        color: #333333;\n        font-size: 24rpx;\n        &:nth-child(3) {\n          margin: 0 20rpx;\n        }\n        &:nth-child(3) {\n          &.active {\n            background: #fef8e7;\n            border: 1px solid #fef8e7;\n          }\n        }\n        &:nth-child(4) {\n          &.active {\n            background: #e7fef8;\n            border: 1px solid #e7fef8;\n          }\n        }\n      }\n\n      .active {\n        background: #e1f1fe;\n        border: 1px solid #e1f1fe;\n      }\n      .addressIcon {\n        width: 24px;\n        height: 24px;\n        display: inline-block;\n        position: absolute;\n        right: 20rpx;\n        top: 0;\n        .icon {\n          width: 16px;\n          height: 16px;\n          display: inline-block;\n          background: url(../../static/toRight.png) no-repeat 6rpx 50%;\n          background-size: contain;\n          transform: rotate(90deg);\n          margin-left: 10rpx;\n        }\n        .iconOn {\n          transform: rotate(-90deg);\n        }\n      }\n\n      .update-input {\n        flex: 1;\n      }\n      .update-adress {\n        position: relative;\n        line-height: 40rpx;\n        padding-bottom: 18rpx;\n      }\n    }\n    // 详细地址\n    .detail {\n      width: 100%;\n      height: 50rpx;\n      margin: 0;\n      /deep/ .uni-place {\n        font-family: PingFangSC, PingFangSC-Regular;\n        font-weight: 400;\n        color: #999999;\n        text-align: left;\n        line-height: 50rpx;\n      }\n    }\n    .detail-ios {\n      padding: 20rpx 14rpx;\n    }\n  }\n  .add_address {\n    margin: 0 auto;\n    button {\n      margin-top: 40rpx;\n      margin: 40rpx 18rpx 0 18rpx;\n      height: 86rpx;\n      line-height: 86rpx;\n      border-radius: 8rpx;\n      opacity: 1;\n      font-size: 30rpx;\n      font-family: PingFangSC, PingFangSC-Medium;\n      font-weight: 600;\n      text-align: center;\n      color: #333333;\n      letter-spacing: 0px;\n      border: 0 none;\n    }\n    .add_btn {\n      background: #ffc200;\n\n      .img_btn {\n        width: 44rpx;\n        height: 44rpx;\n        vertical-align: middle;\n        margin-bottom: 8rpx;\n      }\n    }\n\n    .del_btn {\n      background: #f6f6f6;\n    }\n  }\n}\n\n.customer-box {\n  height: 100vh;\n}\n/deep/ .uni-icons {\n  font-size: 24px !important;\n}\n/deep/ .content-clear-icon {\n  display: inline-block;\n  width: 36rpx;\n  height: 36rpx;\n  line-height: 36rpx;\n  padding-top: 4rpx;\n  padding-bottom: 4rpx;\n}\n</style>", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addOrEditAddress.vue?vue&type=style&index=0&id=174d7646&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addOrEditAddress.vue?vue&type=style&index=0&id=174d7646&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753418841877\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}