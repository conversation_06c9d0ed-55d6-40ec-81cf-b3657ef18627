<view class="container phoneCon data-v-26c6f860"><uni-popup class="popupBox data-v-26c6f860 vue-ref" bind:change="__e" vue-id="5518dcfd-1" data-ref="popup" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-26c6f860"><view class="data-v-26c6f860">{{phoneData}}</view><view data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e" class="data-v-26c6f860">呼叫</view><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="closePopup data-v-26c6f860" bindtap="__e">取消</view></view></uni-popup></view>