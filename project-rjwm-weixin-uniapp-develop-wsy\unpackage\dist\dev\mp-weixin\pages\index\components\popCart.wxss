@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cart_pop.data-v-b2da6f24 {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  height: 60vh;
  background-color: #fff;
  border-radius: 8rpx 8rpx 0 0;
  padding: 20rpx 30rpx 30rpx 30rpx;
  box-sizing: border-box;
}
.cart_pop .top_title.data-v-b2da6f24 {
  display: flex;
  justify-content: space-between;
  border-bottom: solid 1px #ebeef5;
  padding-bottom: 20rpx;
}
.cart_pop .top_title .tit.data-v-b2da6f24 {
  font-size: 40rpx;
  font-weight: bold;
  color: #20232a;
}
.cart_pop .top_title .clear.data-v-b2da6f24 {
  color: #999999;
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  font-family: PingFangSC, PingFangSC-Regular;
}
.cart_pop .top_title .clear .clear_icon.data-v-b2da6f24 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}
.cart_pop .top_title .clear .clear-des.data-v-b2da6f24 {
  height: 56rpx;
  line-height: 56rpx;
}
.cart_pop .card_order_list.data-v-b2da6f24 {
  background-color: #fff;
  padding-top: 40rpx;
  box-sizing: border-box;
  height: calc(100% - 0rpx);
  flex: 1;
  position: relative;
}
.cart_pop .card_order_list .type_item_cont .user_info.data-v-b2da6f24 {
  display: flex;
  margin-bottom: 20rpx;
}
.cart_pop .card_order_list .type_item_cont .user_info .user_avatar.data-v-b2da6f24 {
  margin-right: 20rpx;
}
.cart_pop .card_order_list .type_item_cont .user_info .user_avatar .user_avatar_icon.data-v-b2da6f24 {
  width: 42rpx;
  height: 42rpx;
  border-radius: 42rpx;
}
.cart_pop .card_order_list .type_item_cont .user_info .user_name.data-v-b2da6f24 {
  color: #19232b;
  font-size: 24rpx;
}
.cart_pop .card_order_list .type_item.data-v-b2da6f24 {
  display: flex;
  margin-bottom: 40rpx;
}
.cart_pop .card_order_list .type_item .dish_img.data-v-b2da6f24 {
  width: 128rpx;
  margin-right: 30rpx;
}
.cart_pop .card_order_list .type_item .dish_img .dish_img_url.data-v-b2da6f24 {
  display: block;
  width: 128rpx;
  height: 128rpx;
  border-radius: 8rpx;
}
.cart_pop .card_order_list .type_item .dish_info.data-v-b2da6f24 {
  position: relative;
  flex: 1;
  padding-bottom: 120rpx;
  border-bottom: solid 1px #ebeef5;
}
.cart_pop .card_order_list .type_item .dish_info .dish_name.data-v-b2da6f24 {
  font-size: 32rpx;
  line-height: 40rpx;
  color: #333333;
  font-family: PingFangSC, PingFangSC-Semibold;
  font-weight: 600;
}
.cart_pop .card_order_list .type_item .dish_info .dish_price.data-v-b2da6f24 {
  font-size: 32rpx;
  color: #e94e3c;
  position: absolute;
  bottom: 24rpx;
}
.cart_pop .card_order_list .type_item .dish_info .dish_price .ico.data-v-b2da6f24 {
  font-size: 24rpx;
}
.cart_pop .card_order_list .type_item .dish_info .dish_active.data-v-b2da6f24 {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  display: flex;
}
.cart_pop .card_order_list .type_item .dish_info .dish_active .dish_add.data-v-b2da6f24,
.cart_pop .card_order_list .type_item .dish_info .dish_active .dish_red.data-v-b2da6f24 {
  display: block;
  width: 72rpx;
  height: 72rpx;
}
.cart_pop .card_order_list .type_item .dish_info .dish_active .dish_number.data-v-b2da6f24 {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}
.cart_pop .card_order_list.data-v-b2da6f24::before {
  content: "";
  position: absolute;
  width: 100vw;
  height: 120rpx;
  z-index: 99;
  background: linear-gradient(0deg, white 10%, rgba(255, 255, 255, 0));
  bottom: 0px;
  left: 0px;
}
.cart_pop .card_order_list .seize_seat.data-v-b2da6f24 {
  width: 100%;
  height: 120rpx;
}
.cart_pop .dish_dishFlavor.data-v-b2da6f24 {
  position: absolute;
  left: 0;
  top: 40rpx;
}

