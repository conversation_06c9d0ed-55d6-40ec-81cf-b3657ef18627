{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/products/index.vue?6785", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/products/index.vue?b0b4", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/products/index.vue?24d5", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/products/index.vue?aa28", "uni-app:///pages/products/index.vue", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/products/index.vue?611f", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/products/index.vue?09ca"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "productList", "id", "name", "price", "image", "path", "onLoad", "midManager", "methods", "goBack", "uni", "delta", "goToProduct"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqCjzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC,cACA;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAJ;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;IAEA;EACA;EAEAC;IACA;IACA;MACAC;IACA;EACA;EAEAC;IACAC;MACAC;QAAAC;MAAA;IACA;IAEAC;MACA;MACAL;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAAw/C,CAAgB,s5CAAG,EAAC,C;;;;;;;;;;;ACA5gD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/products/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/products/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=fb16ccc0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=fb16ccc0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fb16ccc0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/products/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=fb16ccc0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.productList, function (product, __i0__) {\n    var $orig = _vm.__get_orig(product)\n    var g0 = product.price.toFixed(2)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"products-list-container\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-back\" @click=\"goBack\">\n        <text class=\"back-icon\">‹</text>\n      </view>\n      <view class=\"nav-title\">商品列表</view>\n    </view>\n    \n    <!-- 商品网格 -->\n    <view class=\"products-grid\">\n      <view\n        class=\"product-card\"\n        v-for=\"product in productList\"\n        :key=\"product.id\"\n        @click=\"goToProduct(product)\"\n      >\n        <view class=\"product-image-wrapper\">\n          <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"product-info\">\n          <view class=\"product-name\">{{ product.name }}</view>\n          <view class=\"product-price\">\n            <text class=\"price-symbol\">¥</text>\n            <text class=\"price-value\">{{ product.price.toFixed(2) }}</text>\n          </view>\n        </view>\n        <view class=\"product-button\">\n          <button class=\"view-button\" @click.stop=\"goToProduct(product)\">查看详情</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport midManager from \"../../utils/midManager.js\"\n\nexport default {\n  data() {\n    return {\n      productList: [\n        { id: 1, name: '精选美味商品1', price: 29.90, image: '/static/logo_ruiji.png', path: '/pages/products/product1/index' },\n        { id: 2, name: '精选美味商品2', price: 35.90, image: '/static/logo_ruiji.png', path: '/pages/products/product2/index' },\n        { id: 3, name: '精选美味商品3', price: 42.90, image: '/static/logo_ruiji.png', path: '/pages/products/product3/index' },\n        { id: 4, name: '精选美味商品4', price: 38.90, image: '/static/logo_ruiji.png', path: '/pages/products/product4/index' },\n        { id: 5, name: '精选美味商品5', price: 45.90, image: '/static/logo_ruiji.png', path: '/pages/products/product5/index' },\n        { id: 6, name: '精选美味商品6', price: 32.90, image: '/static/logo_ruiji.png', path: '/pages/products/product6/index' },\n        { id: 7, name: '精选美味商品7', price: 48.90, image: '/static/logo_ruiji.png', path: '/pages/products/product7/index' },\n        { id: 8, name: '精选美味商品8', price: 36.90, image: '/static/logo_ruiji.png', path: '/pages/products/product8/index' },\n        { id: 9, name: '精选美味商品9', price: 52.90, image: '/static/logo_ruiji.png', path: '/pages/products/product9/index' },\n        { id: 10, name: '精选美味商品10', price: 58.90, image: '/static/logo_ruiji.png', path: '/pages/products/product10/index' }\n      ]\n    }\n  },\n\n  onLoad(options) {\n    // 如果URL中有mid参数，保存它\n    if (options && options.mid) {\n      midManager.setMid(options.mid)\n    }\n  },\n\n  methods: {\n    goBack() {\n      uni.navigateBack({ delta: 1 })\n    },\n\n    goToProduct(product) {\n      // 使用midManager自动添加保存的mid参数\n      midManager.navigateTo(product.path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.products-list-container {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  background-color: #fff;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  \n  .nav-back {\n    width: 60rpx;\n    height: 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    .back-icon {\n      font-size: 40rpx;\n      color: #333;\n      font-weight: bold;\n    }\n  }\n  \n  .nav-title {\n    flex: 1;\n    text-align: center;\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n    margin-right: 60rpx;\n  }\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 30rpx;\n  padding: 30rpx;\n}\n\n.product-card {\n  background-color: #fff;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease;\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.product-image-wrapper {\n  width: 100%;\n  height: 200rpx;\n  overflow: hidden;\n  \n  .product-image {\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.product-info {\n  padding: 20rpx;\n  \n  .product-name {\n    font-size: 28rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 10rpx;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  \n  .product-price {\n    display: flex;\n    align-items: baseline;\n    \n    .price-symbol {\n      font-size: 24rpx;\n      color: #ff6b35;\n      font-weight: bold;\n    }\n    \n    .price-value {\n      font-size: 32rpx;\n      color: #ff6b35;\n      font-weight: bold;\n      margin-left: 5rpx;\n    }\n  }\n}\n\n.product-button {\n  padding: 0 20rpx 20rpx;\n  \n  .view-button {\n    width: 100%;\n    height: 60rpx;\n    background: linear-gradient(135deg, #ff6b35, #ff8c42);\n    color: #fff;\n    font-size: 24rpx;\n    border-radius: 30rpx;\n    border: none;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=fb16ccc0&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=fb16ccc0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753418841612\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}