<view class="product-container data-v-17610972"><view class="product-showcase data-v-17610972"><view class="product-title data-v-17610972">测试店铺1</view><view class="product-subtitle data-v-17610972">测试1</view></view><view class="order-section data-v-17610972"><button data-event-opts="{{[['tap',[['goToOrder',['$event']]]]]}}" class="order-button data-v-17610972" bindtap="__e">立即下单</button></view></view>