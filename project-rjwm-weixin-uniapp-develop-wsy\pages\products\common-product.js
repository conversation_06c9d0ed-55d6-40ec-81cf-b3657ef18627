import midManager from "../../utils/midManager.js"

/**
 * 创建商品页面的通用配置
 * @param {number} productId - 商品ID
 * @param {string} productName - 商品名称
 * @param {number} productPrice - 商品价格
 * @returns {object} - 页面配置对象
 */
export function createProductPageConfig(productId, productName, productPrice) {
  return {
    data() {
      return {
        productInfo: {
          id: productId,
          name: productName,
          price: productPrice,
          image: '/static/logo_ruiji.png',
          description: '这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。'
        }
      }
    },
    
    onLoad(options) {
      console.log(`${productName}页面加载`)
      
      // 如果URL中有mid参数，保存它
      if (options && options.mid) {
        midManager.setMid(options.mid)
      }
    },
    
    methods: {
      // 返回上一页
      goBack() {
        uni.navigateBack({
          delta: 1
        })
      },
      
      // 跳转到下单页面，自动携带保存的mid参数
      goToOrder() {
        midManager.navigateTo('/pages/index/index')
      }
    }
  }
}
