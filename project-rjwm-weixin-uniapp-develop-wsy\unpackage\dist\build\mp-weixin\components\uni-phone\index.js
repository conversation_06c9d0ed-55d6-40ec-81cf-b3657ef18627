(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-phone/index"],{"54e7":function(n,t,e){"use strict";e.r(t);var u=e("7218"),o=e.n(u);for(var i in u)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(i);t["default"]=o.a},7218:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=e("00ba"),o={props:{phoneData:{type:String,default:""}},methods:{call:function(){(0,u.call)(this.phoneData)},closePopup:function(){this.$emit("closePopup")}}};t.default=o},ddaf:function(n,t,e){"use strict";e.r(t);var u=e("fb78"),o=e("54e7");for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);e("3b76");var c=e("828b"),a=Object(c["a"])(o["default"],u["b"],u["c"],!1,null,"26c6f860",null,!1,u["a"],void 0);t["default"]=a.exports},fb78:function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return u}));var u={uniPopup:function(){return e.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(e.bind(null,"c554"))}},o=function(){var n=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-phone/index-create-component',
    {
        'components/uni-phone/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("ddaf"))
        })
    },
    [['components/uni-phone/index-create-component']]
]);
