{"version": 3, "sources": ["webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/common/simple-address/simple-address.nvue?eb10", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/common/simple-address/simple-address.nvue?2be1", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/common/simple-address/simple-address.nvue?17bc", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/common/simple-address/simple-address.nvue?a4c3", "uni-app:///pages/common/simple-address/simple-address.nvue", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/common/simple-address/simple-address.nvue?1594", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/common/simple-address/simple-address.nvue?3023"], "names": ["name", "props", "animation", "type", "default", "maskClick", "show", "maskBgColor", "themeColor", "<PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "ani", "showPopup", "picker<PERSON><PERSON><PERSON>", "provinceDataList", "cityDataList", "areaDataList", "watch", "created", "methods", "init", "handPick<PERSON><PERSON><PERSON>Default", "picker<PERSON><PERSON><PERSON>", "changePickerValue", "areaData", "_$emit", "label", "value", "cityCode", "areaCode", "provinceCode", "_get<PERSON><PERSON>l", "_getCityCode", "_getProvinceCode", "_getAreaCode", "clear", "hideMask", "pickerCancel", "pickerConfirm", "open", "setTimeout", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuyB,CAAgB,yxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuD3zB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;;IACAI;MACAL;MACAC;IACA;;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAX;MACA;QACA;MACA;QACA;MACA;IACA;IACAG;MACA;IACA;EACA;EACAS;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;QACAA;MACA;QACA;QACA,oBACAC;QACAD;MACA;MACA;MACA;IACA;IACAE;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA,kBACA,mDACA,MACA,+CACA,MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC,yBAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;UACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAD;UACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChOA;AAAA;AAAA;AAAA;AAAkgD,CAAgB,g6CAAG,EAAC,C;;;;;;;;;;;ACAthD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/common/simple-address/simple-address.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./simple-address.nvue?vue&type=template&id=6d425f7a&scoped=true&\"\nvar renderjs\nimport script from \"./simple-address.nvue?vue&type=script&lang=js&\"\nexport * from \"./simple-address.nvue?vue&type=script&lang=js&\"\nimport style0 from \"./simple-address.nvue?vue&type=style&index=0&id=6d425f7a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6d425f7a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/common/simple-address/simple-address.nvue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./simple-address.nvue?vue&type=template&id=6d425f7a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./simple-address.nvue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./simple-address.nvue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"simple-address\" v-if=\"showPopup\" @touchmove.stop.prevent=\"clear\">\n\t\t<!-- 遮罩层 -->\n\t\t<view class=\"simple-address-mask\" @touchmove.stop.prevent=\"clear\" v-if=\"maskClick\" :class=\"[ani+'-mask', animation ? 'mask-ani' : '']\" :style=\"{\n\t\t\t\t\t'background-color': maskBgColor\n\t\t\t\t}\"\n\t\t @tap=\"hideMask(true)\"></view>\n\n\t\t<view class=\"simple-address-content simple-address--fixed\" :class=\"[type,ani+'-content', animation ? 'content-ani' : '']\">\n\t\t\t<view class=\"simple-address__header\">\n\t\t\t\t<view class=\"simple-address__header-btn-box\" @click=\"pickerCancel\">\n\t\t\t\t\t<text class=\"simple-address__header-text\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"simple-address__header-btn-box\" @click=\"pickerConfirm\">\n\t\t\t\t\t<text class=\"simple-address__header-text\" :style=\"{color:themeColor}\">确定</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"simple-address__box\">\n\t\t\t\t<picker-view indicator-style=\"height: 70rpx;\" class=\"simple-address-view\" :value=\"pickerValue\" @change=\"pickerChange\">\n\n\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"(item,index) in provinceDataList\" :key=\"index\">{{item.label}}</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t<text class=\"picker-item\" v-for=\"(item,index) in provinceDataList\" :key=\"index\">{{item.label}}</text>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"(item,index) in cityDataList\" :key=\"index\">{{item.label}}</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t<text class=\"picker-item\" v-for=\"(item,index) in cityDataList\" :key=\"index\">{{item.label}}</text>\n\t\t\t\t\t\t<!-- #endif -->\n\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"(item,index) in areaDataList\" :key=\"index\">{{item.label}}</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t<text class=\"picker-item\" v-for=\"(item,index) in areaDataList\" :key=\"index\">{{item.label}}</text>\n\t\t\t\t\t\t<!-- #endif -->\n\n\t\t\t\t\t</picker-view-column>\n\n\t\t\t\t</picker-view>\n\t\t\t</view>\n\t\t</view>\n\n\t</view>\n</template>\n\n<script>\n\timport provinceData from './city-data/province.js';\n\timport cityData from './city-data/city.js';\n\timport areaData from './city-data/area.js';\n\texport default {\n\t\tname: \"simpleAddress\",\n\t\tprops: {\n\t\t\t// 开启动画\n\t\t\tanimation: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t/* 弹出层类型，可选值；\n\t\t\t\tbottom：底部弹出层\n\t\t\t*/\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'bottom'\n\t\t\t},\n\t\t\t// maskClick\n\t\t\tmaskClick: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tshow: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tmaskBgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'rgba(0, 0, 0, 0.4)', //背景颜色 rgba(0, 0, 0, 0.4) 为空则调用 uni.scss\n\t\t\t},\n\t\t\tthemeColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '', // 主题色\n\t\t\t},\n\t\t\t/* 默认值 */\n\t\t\tpickerValueDefault: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn [0, 0, 0]\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tani: '',\n\t\t\t\tshowPopup: false,\n\t\t\t\tpickerValue: [0, 0, 0],\n\t\t\t\tprovinceDataList: [],\n\t\t\t\tcityDataList: [],\n\t\t\t\tareaDataList: [],\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tshow(newValue) {\n\t\t\t\tif (newValue) {\n\t\t\t\t\tthis.open()\n\t\t\t\t} else {\n\t\t\t\t\tthis.close()\n\t\t\t\t}\n\t\t\t},\n\t\t\tpickerValueDefault() {\n\t\t\t\tthis.init();\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tthis.handPickValueDefault(); // 对 pickerValueDefault 做兼容处理\n\t\t\t\tthis.provinceDataList = provinceData;\n\t\t\t\tthis.cityDataList = cityData[this.pickerValueDefault[0]];\n\t\t\t\tthis.areaDataList = areaData[this.pickerValueDefault[0]][this.pickerValueDefault[1]];\n\t\t\t\tthis.pickerValue = this.pickerValueDefault;\n\t\t\t},\n\t\t\thandPickValueDefault() {\n\t\t\t\tif (this.pickerValueDefault !== [0, 0, 0]) {\n\t\t\t\t\tif (this.pickerValueDefault[0] > provinceData.length - 1) {\n\t\t\t\t\t\tthis.pickerValueDefault[0] = provinceData.length - 1;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.pickerValueDefault[1] > cityData[this.pickerValueDefault[0]].length - 1) {\n\t\t\t\t\t\tthis.pickerValueDefault[1] = cityData[this.pickerValueDefault[0]].length - 1;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.pickerValueDefault[2] > areaData[this.pickerValueDefault[0]][this.pickerValueDefault[1]].length - 1) {\n\t\t\t\t\t\tthis.pickerValueDefault[2] = areaData[this.pickerValueDefault[0]][this.pickerValueDefault[1]].length - 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tpickerChange(e) {\n\t\t\t\tlet changePickerValue = e.detail.value;\n\t\t\t\tif (this.pickerValue[0] !== changePickerValue[0]) {\n\t\t\t\t\t// 第一级发生滚动\n\t\t\t\t\tthis.cityDataList = cityData[changePickerValue[0]];\n\t\t\t\t\tthis.areaDataList = areaData[changePickerValue[0]][0];\n\t\t\t\t\tchangePickerValue[1] = 0;\n\t\t\t\t\tchangePickerValue[2] = 0;\n\t\t\t\t} else if (this.pickerValue[1] !== changePickerValue[1]) {\n\t\t\t\t\t// 第二级滚动\n\t\t\t\t\tthis.areaDataList =\n\t\t\t\t\t\tareaData[changePickerValue[0]][changePickerValue[1]];\n\t\t\t\t\tchangePickerValue[2] = 0;\n\t\t\t\t}\n\t\t\t\tthis.pickerValue = changePickerValue;\n\t\t\t\tthis._$emit('onChange');\n\t\t\t},\n\t\t\t_$emit(emitName) {\n\t\t\t\tlet pickObj = {\n\t\t\t\t\tlabel: this._getLabel(),\n\t\t\t\t\tvalue: this.pickerValue,\n\t\t\t\t\tcityCode: this._getCityCode(),\n\t\t\t\t\tareaCode: this._getAreaCode(),\n\t\t\t\t\tprovinceCode: this._getProvinceCode()\n\t\t\t\t};\n\t\t\t\tthis.$emit(emitName, pickObj);\n\t\t\t},\n\t\t\t_getLabel() {\n\t\t\t\tlet pcikerLabel =\n\t\t\t\t\tthis.provinceDataList[this.pickerValue[0]].label +\n\t\t\t\t\t'/' +\n\t\t\t\t\tthis.cityDataList[this.pickerValue[1]].label +\n\t\t\t\t\t'/' +\n\t\t\t\t\tthis.areaDataList[this.pickerValue[2]].label;\n\t\t\t\treturn pcikerLabel;\n\t\t\t},\n\t\t\t_getCityCode() {\n\t\t\t\treturn this.cityDataList[this.pickerValue[1]].value;\n\t\t\t},\n\t\t\t_getProvinceCode() {\n\t\t\t\treturn this.provinceDataList[this.pickerValue[0]].value;\n\t\t\t},\n\t\t\t_getAreaCode() {\n\t\t\t\treturn this.areaDataList[this.pickerValue[2]].value;\n\t\t\t},\n\t\t\tclear() {\n\n\t\t\t},\n\t\t\thideMask() {\n\t\t\t\tthis._$emit('onCancel');\n\t\t\t\tthis.close();\n\t\t\t},\n\t\t\tpickerCancel() {\n\t\t\t\tthis._$emit('onCancel');\n\t\t\t\tthis.close();\n\t\t\t},\n\t\t\tpickerConfirm() {\n\t\t\t\tthis._$emit('onConfirm');\n\t\t\t\tthis.close();\n\t\t\t},\n\t\t\topen() {\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.ani = 'simple-' + this.type\n\t\t\t\t\t}, 100)\n\t\t\t\t})\n\t\t\t\tthis.$emit('isClass',true)\n\t\t\t},\n\t\t\tclose(type) {\n\t\t\t\tif (!this.maskClick && type) return;\n\t\t\t\tthis.ani = ''\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.showPopup = false\n\t\t\t\t\t}, 300)\n\t\t\t\t})\n\t\t\t\tthis.$emit('isClass',false)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.simple-address {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: column;\n\t}\n\n\t.simple-address-mask {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\n\t\ttransition-property: opacity;\n\t\ttransition-duration: 0.3s;\n\t\topacity: 0;\n\t\t/* #ifndef APP-NVUE */\n\t\tz-index: 99;\n\t\t/* #endif */\n\t}\n\n\n\t.mask-ani {\n\t\ttransition-property: opacity;\n\t\ttransition-duration: 0.2s;\n\t}\n\n\t.simple-bottom-mask {\n\t\topacity: 1;\n\t}\n\n\t.simple-center-mask {\n\t\topacity: 1;\n\t}\n\n\t.simple-address--fixed {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\ttransition-property: transform;\n\t\ttransition-duration: 0.3s;\n\t\ttransform: translateY(460rpx);\n\t\t/* #ifndef APP-NVUE */\n\t\tz-index: 99;\n\t\t/* #endif */\n\t}\n\n\t.simple-address-content {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.simple-content-bottom {\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\ttransform: translateY(500rpx);\n\t}\n\n\t.content-ani {\n\t\ttransition-property: transform, opacity;\n\t\ttransition-duration: 0.2s;\n\t}\n\n\t.simple-bottom-content {\n\t\ttransform: translateY(0);\n\t}\n\n\t.simple-center-content {\n\t\ttransform: scale(1);\n\t\topacity: 1;\n\t}\n\n\t.simple-address__header {\n\t\tposition: relative;\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\t\tjustify-content: space-between;\n\t\tborder-bottom-color: #f2f2f2;\n\t\tborder-bottom-style: solid;\n\t\tborder-bottom-width: 1rpx;\n\t}\n\n\t.simple-address--fixed-top {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\tborder-top-color: $uni-border-color;\n\t\tborder-top-style: solid;\n\t\tborder-top-width: 1rpx;\n\t}\n\n\t.simple-address__header-btn-box {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\theight: 100rpx;\n\t}\n\n\t.simple-address__header-text {\n\t\ttext-align: center;\n\t\tfont-size: $uni-font-size-base;\n\t\tcolor: #666;\n\t\tline-height: 70rpx;\n\t\tpadding-left: 40rpx;\n\t\tpadding-right: 40rpx;\n\t}\n\n\t.simple-address__box {\n\t\tposition: relative;\n\t}\n\n\t.simple-address-view {\n\t\tposition: relative;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\t/* #ifndef APP-NVUE */\n\t\twidth: 100%;\n\t\t/* #endif */\n\t\t/* #ifdef APP-NVUE */\n\t\twidth: 750rpx;\n\t\t/* #endif */\n\t\theight: 408rpx;\n\t\tbackground-color: rgba(255, 255, 255, 1);\n\t}\n\n\t.picker-item {\n\t\ttext-align: center;\n\t\tline-height: 70rpx;\n\t\ttext-overflow: ellipsis;\n\t\tfont-size: 28rpx;\n\t}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./simple-address.nvue?vue&type=style&index=0&id=6d425f7a&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./simple-address.nvue?vue&type=style&index=0&id=6d425f7a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753418842114\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}