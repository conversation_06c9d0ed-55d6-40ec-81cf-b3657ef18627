<view class="data-v-0ca91b30"><uni-nav-bar vue-id="3bc35b9e-1" left-icon="back" leftIcon="arrowleft" title="提交订单" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-0ca91b30" bind:__l="__l"></uni-nav-bar><view data-event-opts="{{[['touchstart',[['touchstart',['$event']]]]]}}" class="order_content data-v-0ca91b30" bindtouchstart="__e"><view class="order_content_box data-v-0ca91b30"><address-pop vue-id="3bc35b9e-2" address="{{address}}" tagLabel="{{tagLabel}}" addressLabel="{{addressLabel}}" nickName="{{nickName}}" phoneNumber="{{phoneNumber}}" arrivalTime="{{arrivalTime}}" popleft="{{popleft}}" weeks="{{weeks}}" newDateData="{{newDateData}}" tabIndex="{{tabIndex}}" selectValue="{{selectValue}}" data-event-opts="{{[['^change',[['change']]],['^goAddress',[['goAddress']]],['^dateChange',[['dateChange']]],['^timeClick',[['timeClick']]]]}}" bind:change="__e" bind:goAddress="__e" bind:dateChange="__e" bind:timeClick="__e" class="data-v-0ca91b30" bind:__l="__l"></address-pop><view class="order_list_cont data-v-0ca91b30"><dish-detail vue-id="3bc35b9e-3" orderDataes="{{orderDataes}}" showDisplay="{{showDisplay}}" orderDishNumber="{{orderDishNumber}}" orderListDataes="{{orderListDataes}}" orderDishPrice="{{orderDishPrice}}" class="data-v-0ca91b30" bind:__l="__l"></dish-detail><view class="boxPad data-v-0ca91b30"><dish-info vue-id="3bc35b9e-4" remark="{{remark}}" tablewareData="{{tablewareData}}" radioGroup="{{radioGroup}}" activeRadio="{{activeRadio}}" baseData="{{baseData}}" data-ref="dishinfo" data-event-opts="{{[['^goRemark',[['goRemark']]],['^openPopuos',[['openPopuos']]],['^change',[['change']]],['^closePopup',[['closePopup']]],['^handlePiker',[['handlePiker']]],['^changeCont',[['changeCont']]],['^handleRadio',[['handleRadio']]]]}}" bind:goRemark="__e" bind:openPopuos="__e" bind:change="__e" bind:closePopup="__e" bind:handlePiker="__e" bind:changeCont="__e" bind:handleRadio="__e" class="data-v-0ca91b30 vue-ref" bind:__l="__l"></dish-info></view></view></view><view class="footer_order_buttom order_form data-v-0ca91b30"><view class="order_number data-v-0ca91b30"><image class="order_number_icon data-v-0ca91b30" src="../../static/btn_waiter_sel.png" mode></image><view class="order_dish_num data-v-0ca91b30">{{''+orderDishNumber+''}}</view></view><view class="order_price data-v-0ca91b30"><text class="ico data-v-0ca91b30">￥</text>{{''+$root.g0+''}}</view><view class="order_but data-v-0ca91b30"><block wx:if="{{isHandlePy}}"><view class="order_but_rit data-v-0ca91b30">去支付</view></block><block wx:else><view data-event-opts="{{[['tap',[['payOrderHandle']]]]}}" class="order_but_rit data-v-0ca91b30" bindtap="__e">去支付</view></block></view></view></view></view>