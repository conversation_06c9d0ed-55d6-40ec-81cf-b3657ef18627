@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-badge--x {
  display: inline-block;
  position: relative;
}
.uni-badge--absolute {
  position: absolute;
}
.uni-badge--small {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}
.uni-badge {
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-font-feature-settings: "tnum";
          font-feature-settings: "tnum";
  min-width: 20px;
  justify-content: center;
  flex-direction: row;
  height: 20px;
  padding: 0 4px;
  line-height: 18px;
  color: #fff;
  border-radius: 100px;
  background-color: #909399;
  background-color: transparent;
  border: 1px solid #fff;
  text-align: center;
  font-family: 'Helvetica Neue', Helvetica, sans-serif;
  font-size: 12px;
}
.uni-badge--info {
  color: #fff;
  background-color: #909399;
}
.uni-badge--primary {
  background-color: #2979ff;
}
.uni-badge--success {
  background-color: #4cd964;
}
.uni-badge--warning {
  background-color: #f0ad4e;
}
.uni-badge--error {
  background-color: #dd524d;
}
.uni-badge--inverted {
  padding: 0 5px 0 0;
  color: #909399;
}
.uni-badge--info-inverted {
  color: #909399;
  background-color: transparent;
}
.uni-badge--primary-inverted {
  color: #2979ff;
  background-color: transparent;
}
.uni-badge--success-inverted {
  color: #4cd964;
  background-color: transparent;
}
.uni-badge--warning-inverted {
  color: #f0ad4e;
  background-color: transparent;
}
.uni-badge--error-inverted {
  color: #dd524d;
  background-color: transparent;
}

