(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/details/components/deliveryInfo"],{"1ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},3672:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{orderDetailsData:{type:Object,default:function(){return{}}}},computed:{cryptoName:function(){return this.orderDetailsData.consignee?0==this.orderDetailsData.sex?this.orderDetailsData.consignee.charAt(0)+"先生":this.orderDetailsData.consignee.charAt(0)+"女士":""}}};e.default=a},"3f20":function(t,e,n){"use strict";n.r(e);var a=n("1ba8"),r=n("f413");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("a457");var o=n("828b"),u=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=u.exports},f413:function(t,e,n){"use strict";n.r(e);var a=n("3672"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/details/components/deliveryInfo-create-component',
    {
        'pages/details/components/deliveryInfo-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3f20"))
        })
    },
    [['pages/details/components/deliveryInfo-create-component']]
]);
