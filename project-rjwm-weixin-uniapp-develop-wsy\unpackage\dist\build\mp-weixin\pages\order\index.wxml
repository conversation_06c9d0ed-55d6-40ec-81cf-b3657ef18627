<view class="data-v-4f88a28e"><uni-nav-bar vue-id="3bc35b9e-1" left-icon="back" leftIcon="arrowleft" title="提交订单" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-4f88a28e" bind:__l="__l"></uni-nav-bar><view data-event-opts="{{[['touchstart',[['touchstart',['$event']]]]]}}" class="order_content data-v-4f88a28e" bindtouchstart="__e"><view class="order_content_box data-v-4f88a28e"><address-pop vue-id="3bc35b9e-2" address="{{address}}" tagLabel="{{tagLabel}}" addressLabel="{{addressLabel}}" nickName="{{nickName}}" phoneNumber="{{phoneNumber}}" arrivalTime="{{arrivalTime}}" popleft="{{popleft}}" weeks="{{weeks}}" newDateData="{{newDateData}}" tabIndex="{{tabIndex}}" selectValue="{{selectValue}}" data-event-opts="{{[['^change',[['change']]],['^goAddress',[['goAddress']]],['^dateChange',[['dateChange']]],['^timeClick',[['timeClick']]]]}}" bind:change="__e" bind:goAddress="__e" bind:dateChange="__e" bind:timeClick="__e" class="data-v-4f88a28e" bind:__l="__l"></address-pop><view class="order_list_cont data-v-4f88a28e"><dish-detail vue-id="3bc35b9e-3" orderDataes="{{orderDataes}}" showDisplay="{{showDisplay}}" orderDishNumber="{{orderDishNumber}}" orderListDataes="{{orderListDataes}}" orderDishPrice="{{orderDishPrice}}" class="data-v-4f88a28e" bind:__l="__l"></dish-detail><view class="boxPad data-v-4f88a28e"><dish-info vue-id="3bc35b9e-4" remark="{{remark}}" tablewareData="{{tablewareData}}" radioGroup="{{radioGroup}}" activeRadio="{{activeRadio}}" baseData="{{baseData}}" data-ref="dishinfo" data-event-opts="{{[['^goRemark',[['goRemark']]],['^openPopuos',[['openPopuos']]],['^change',[['change']]],['^closePopup',[['closePopup']]],['^handlePiker',[['handlePiker']]],['^changeCont',[['changeCont']]],['^handleRadio',[['handleRadio']]]]}}" bind:goRemark="__e" bind:openPopuos="__e" bind:change="__e" bind:closePopup="__e" bind:handlePiker="__e" bind:changeCont="__e" bind:handleRadio="__e" class="data-v-4f88a28e vue-ref" bind:__l="__l"></dish-info></view></view></view><view class="footer_order_buttom order_form data-v-4f88a28e"><view class="order_number data-v-4f88a28e"><image class="order_number_icon data-v-4f88a28e" src="../../static/btn_waiter_sel.png" mode></image><view class="order_dish_num data-v-4f88a28e">{{''+orderDishNumber+''}}</view></view><view class="order_price data-v-4f88a28e"><text class="ico data-v-4f88a28e">￥</text>{{''+$root.g0+''}}</view><view class="order_but data-v-4f88a28e"><block wx:if="{{isHandlePy}}"><view class="order_but_rit data-v-4f88a28e">去支付</view></block><block wx:else><view data-event-opts="{{[['tap',[['payOrderHandle']]]]}}" class="order_but_rit data-v-4f88a28e" bindtap="__e">去支付</view></block></view></view></view></view>