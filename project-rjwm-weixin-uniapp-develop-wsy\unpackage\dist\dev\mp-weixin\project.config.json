{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": false, "postcss": false, "minified": true, "newFeature": true, "bigPackageSizeSupport": true}, "compileType": "miniprogram", "libVersion": "", "appid": "wx2562652ea619766f", "projectname": "sky-take-out-user-mp", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": 0, "list": [{"name": "历史订单", "query": "", "id": 0, "pathName": "pages/historyOrder/historyOrder"}]}}}