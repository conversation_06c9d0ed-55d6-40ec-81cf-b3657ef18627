<view class="data-v-106e789b"><uni-nav-bar vue-id="45983bea-1" left-icon="back" leftIcon="arrowleft" title="订单详情" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-106e789b" bind:__l="__l"></uni-nav-bar><view class="order_content orderDetail data-v-106e789b"><view class="order_content_box data-v-106e789b" scroll-y="true" scroll-top="0rpx"><status vue-id="45983bea-2" timeout="{{timeout}}" orderDetailsData="{{orderDetailsData}}" rocallTime="{{rocallTime}}" data-ref="status" data-event-opts="{{[['^statusWord',[['statusWord']]],['^paymentTime',[['paymentTime']]],['^handlePay',[['handlePay']]],['^handleReminder',[['handleReminder']]],['^handleRefund',[['handleRefund']]]]}}" bind:statusWord="__e" bind:paymentTime="__e" bind:handlePay="__e" bind:handleReminder="__e" bind:handleRefund="__e" class="data-v-106e789b vue-ref" bind:__l="__l"></status><order-detail vue-id="45983bea-3" orderDataes="{{orderDataes}}" orderDetailsData="{{orderDetailsData}}" showDisplay="{{showDisplay}}" class="data-v-106e789b" bind:__l="__l"></order-detail><view class="box contactMerchant data-v-106e789b"><button data-event-opts="{{[['tap',[['handlePhone',['bottom','$0'],['orderDetailsData.shopTelephone']]]]]}}" bindtap="__e" class="data-v-106e789b"><view class="phoneIcon data-v-106e789b"></view>联系商家</button><block wx:if="{{$root.g0}}"><button data-event-opts="{{[['tap',[['handlePhone',['bottom','$0'],['orderDetailsData.courierTelephone']]]]]}}" class="call-rider data-v-106e789b" bindtap="__e"><view class="phoneIcon data-v-106e789b"></view>联系骑手</button></block></view><delivery-info vue-id="45983bea-4" orderDetailsData="{{orderDetailsData}}" class="data-v-106e789b" bind:__l="__l"></delivery-info><order-info vue-id="45983bea-5" orderDetailsData="{{orderDetailsData}}" class="data-v-106e789b" bind:__l="__l"></order-info></view><uni-popup class="comPopupBox data-v-106e789b vue-ref" vue-id="45983bea-6" data-ref="commonPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-106e789b"><view class="text data-v-106e789b">{{textTip}}</view><block wx:if="{{showConfirm}}"><view class="btn data-v-106e789b"><view data-event-opts="{{[['tap',[['closePopupInfo',['$event']]]]]}}" bindtap="__e" class="data-v-106e789b">确认</view></view></block><block wx:else><view class="btn data-v-106e789b"><view data-event-opts="{{[['tap',[['closePopupInfo',['$event']]]]]}}" bindtap="__e" class="data-v-106e789b">先等等</view><view data-event-opts="{{[['tap',[['handlePhone',['bottom']]]]]}}" bindtap="__e" class="data-v-106e789b">拨打电话</view></view></block></view></uni-popup><view class="container phoneCon data-v-106e789b"><uni-popup class="popupBox data-v-106e789b vue-ref" bind:change="__e" vue-id="45983bea-7" data-ref="phone" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-106e789b"><view class="data-v-106e789b">{{phone}}</view><view data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e" class="data-v-106e789b">呼叫</view><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="closePopup data-v-106e789b" bindtap="__e">取消</view></view></uni-popup></view></view></view>