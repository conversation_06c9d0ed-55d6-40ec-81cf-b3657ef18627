{"version": 3, "sources": ["webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?9724", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?a389", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?ffe2", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?1896", "uni-app:///uni_modules/uni-popup/components/uni-popup/uni-popup.vue", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?33fc", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?3bc2"], "names": ["name", "components", "emits", "props", "animation", "type", "default", "isMaskClick", "maskClick", "backgroundColor", "safeArea", "maskBackgroundColor", "watch", "handler", "immediate", "isDesktop", "showPopup", "data", "duration", "ani", "showTrans", "popup<PERSON><PERSON><PERSON>", "popupHeight", "config", "top", "bottom", "center", "left", "right", "message", "dialog", "share", "maskClass", "position", "transClass", "maskShow", "mkclick", "popupstyle", "computed", "bg", "mounted", "uni", "windowWidth", "windowHeight", "windowTop", "screenHeight", "safeAreaInsets", "fixSize", "destroyed", "created", "methods", "setH5Visible", "closeMask", "disableMask", "clear", "e", "open", "direction", "console", "show", "close", "clearTimeout", "touchstart", "onTap", "paddingBottom", "display", "flexDirection", "justifyContent", "alignItems"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAiyB,CAAgB,mxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuBrzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA,eAuBA;EACAA;EACAC,aAIA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EAEAM;IACA;AACA;AACA;IACAP;MACAQ;QACA;QACA;MACA;MACAC;IACA;IACAC;MACAF;QACA;QACA;MACA;MACAC;IACA;IACA;AACA;AACA;AACA;IACAN;MACAK;QACA;MACA;MACAC;IACA;IACAP;MACAM;QACA;MACA;MACAC;IACA;IACA;IACAE,qCAKA;EACA;EACAC;IACA;MACAC;MACAC;MACAH;MACAI;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAR;QACAD;QACAG;QACAC;QACAnB;MACA;MACAyB;QACAD;QACAN;QACAC;MACA;MACAO;MACAC;MACAC;IACA;EACA;EACAC;IACAvB;MACA;IACA;IACAwB;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA,4BAOAC;QANAC;QACAC;QACAC;QACAlC;QACAmC;QACAC;MAEA;MACA;MACA;MACA;QAEA;MAKA;QACA;MACA;IACA;IACAC;EAOA;EAEA;EACAC;IACA;EACA;EAQAC;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC,uCAKA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MAEAC;MAEA;IACA;IAEAC;MACA;MACA;QACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;QACAC;QACAtD;MACA;IACA;IACAuD;MAAA;MACA;MACA;QACAD;QACAtD;MACA;MACAwD;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAvC;MAAA;MACA;MACA;MACA;QACAS;QACAN;QACAC;QACAnB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAgB;MACA;MACA;MACA;QACAQ;QACAN;QACAC;QACAH;QACAuC;QACAvD;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAiB;MACA;MACA;MACA;QACAO;QAEAgC;QACAC;QAEAzC;QACAE;QACAC;QACAJ;QACA2C;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAzC;MACA;MACA;MACA;QACAM;QACAN;QACAF;QACAD;QACAf;QAEAwD;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;IACAtC;MACA;MACA;MACA;QACAK;QACAR;QACAG;QACAJ;QACAf;QAEAwD;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnaA;AAAA;AAAA;AAAA;AAAo+C,CAAgB,k4CAAG,EAAC,C;;;;;;;;;;;ACAx/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-popup/components/uni-popup/uni-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-popup.vue?vue&type=template&id=7c43d41b&\"\nvar renderjs\nimport script from \"./uni-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-popup.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=template&id=7c43d41b&\"", "var components\ntry {\n  components = {\n    uniTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-transition/components/uni-transition/uni-transition\" */ \"@/uni_modules/uni-transition/components/uni-transition/uni-transition.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"showPopup\" class=\"uni-popup\" :class=\"[popupstyle, isDesktop ? 'fixforpc-z-index' : '']\">\n\t\t<view @touchstart=\"touchstart\">\n\t\t\t<uni-transition key=\"1\" v-if=\"maskShow\" name=\"mask\" mode-class=\"fade\" :styles=\"maskClass\"\n\t\t\t\t:duration=\"duration\" :show=\"showTrans\" @click=\"onTap\" />\n\t\t\t<uni-transition key=\"2\" :mode-class=\"ani\" name=\"content\" :styles=\"transClass\" :duration=\"duration\"\n\t\t\t\t:show=\"showTrans\" @click=\"onTap\">\n\t\t\t\t<view class=\"uni-popup__wrapper\" :style=\"{ backgroundColor: bg }\" :class=\"[popupstyle]\" @click=\"clear\">\n\t\t\t\t\t<slot />\n\t\t\t\t</view>\n\t\t\t</uni-transition>\n\t\t</view>\n\t\t<!-- #ifdef H5 -->\n\t\t<keypress v-if=\"maskShow\" @esc=\"onTap\" />\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<script>\n\t// #ifdef H5\n\timport keypress from './keypress.js'\n\t// #endif\n\n\t/**\n\t * PopUp 弹出层\n\t * @description 弹出层组件，为了解决遮罩弹层的问题\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=329\n\t * @property {String} type = [top|center|bottom|left|right|message|dialog|share] 弹出方式\n\t * \t@value top 顶部弹出\n\t * \t@value center 中间弹出\n\t * \t@value bottom 底部弹出\n\t * \t@value left\t\t左侧弹出\n\t * \t@value right  右侧弹出\n\t * \t@value message 消息提示\n\t * \t@value dialog 对话框\n\t * \t@value share 底部分享示例\n\t * @property {Boolean} animation = [true|false] 是否开启动画\n\t * @property {Boolean} maskClick = [true|false] 蒙版点击是否关闭弹窗(废弃)\n\t * @property {Boolean} isMaskClick = [true|false] 蒙版点击是否关闭弹窗\n\t * @property {String}  backgroundColor 主窗口背景色\n\t * @property {String}  maskBackgroundColor 蒙版颜色\n\t * @property {Boolean} safeArea\t\t   是否适配底部安全区\n\t * @event {Function} change 打开关闭弹窗触发，e={show: false}\n\t * @event {Function} maskClick 点击遮罩触发\n\t */\n\n\texport default {\n\t\tname: 'uniPopup',\n\t\tcomponents: {\n\t\t\t// #ifdef H5\n\t\t\tkeypress\n\t\t\t// #endif\n\t\t},\n\t\temits: ['change', 'maskClick'],\n\t\tprops: {\n\t\t\t// 开启动画\n\t\t\tanimation: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\n\t\t\t// message: 消息提示 ; dialog : 对话框\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'center'\n\t\t\t},\n\t\t\t// maskClick\n\t\t\tisMaskClick: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: null\n\t\t\t},\n\t\t\t// TODO 2 个版本后废弃属性 ，使用 isMaskClick\n\t\t\tmaskClick: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: null\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'none'\n\t\t\t},\n\t\t\tsafeArea: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tmaskBackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'rgba(0, 0, 0, 0.4)'\n\t\t\t},\n\t\t},\n\n\t\twatch: {\n\t\t\t/**\n\t\t\t * 监听type类型\n\t\t\t */\n\t\t\ttype: {\n\t\t\t\thandler: function(type) {\n\t\t\t\t\tif (!this.config[type]) return\n\t\t\t\t\tthis[this.config[type]](true)\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tisDesktop: {\n\t\t\t\thandler: function(newVal) {\n\t\t\t\t\tif (!this.config[newVal]) return\n\t\t\t\t\tthis[this.config[this.type]](true)\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\t/**\n\t\t\t * 监听遮罩是否可点击\n\t\t\t * @param {Object} val\n\t\t\t */\n\t\t\tmaskClick: {\n\t\t\t\thandler: function(val) {\n\t\t\t\t\tthis.mkclick = val\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tisMaskClick: {\n\t\t\t\thandler: function(val) {\n\t\t\t\t\tthis.mkclick = val\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\t// H5 下禁止底部滚动\n\t\t\tshowPopup(show) {\n\t\t\t\t// #ifdef H5\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = show ? 'hidden' : 'visible'\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tduration: 300,\n\t\t\t\tani: [],\n\t\t\t\tshowPopup: false,\n\t\t\t\tshowTrans: false,\n\t\t\t\tpopupWidth: 0,\n\t\t\t\tpopupHeight: 0,\n\t\t\t\tconfig: {\n\t\t\t\t\ttop: 'top',\n\t\t\t\t\tbottom: 'bottom',\n\t\t\t\t\tcenter: 'center',\n\t\t\t\t\tleft: 'left',\n\t\t\t\t\tright: 'right',\n\t\t\t\t\tmessage: 'top',\n\t\t\t\t\tdialog: 'center',\n\t\t\t\t\tshare: 'bottom'\n\t\t\t\t},\n\t\t\t\tmaskClass: {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, 0.4)'\n\t\t\t\t},\n\t\t\t\ttransClass: {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0\n\t\t\t\t},\n\t\t\t\tmaskShow: true,\n\t\t\t\tmkclick: true,\n\t\t\t\tpopupstyle: this.isDesktop ? 'fixforpc-top' : 'top'\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tisDesktop() {\n\t\t\t\treturn this.popupWidth >= 500 && this.popupHeight >= 500\n\t\t\t},\n\t\t\tbg() {\n\t\t\t\tif (this.backgroundColor === '' || this.backgroundColor === 'none') {\n\t\t\t\t\treturn 'transparent'\n\t\t\t\t}\n\t\t\t\treturn this.backgroundColor\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tconst fixSize = () => {\n\t\t\t\tconst {\n\t\t\t\t\twindowWidth,\n\t\t\t\t\twindowHeight,\n\t\t\t\t\twindowTop,\n\t\t\t\t\tsafeArea,\n\t\t\t\t\tscreenHeight,\n\t\t\t\t\tsafeAreaInsets\n\t\t\t\t} = uni.getSystemInfoSync()\n\t\t\t\tthis.popupWidth = windowWidth\n\t\t\t\tthis.popupHeight = windowHeight + (windowTop || 0)\n\t\t\t\t// TODO fix by mehaotian 是否适配底部安全区 ,目前微信ios 、和 app ios 计算有差异，需要框架修复\n\t\t\t\tif (safeArea && this.safeArea) {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tthis.safeAreaInsets = screenHeight - safeArea.bottom\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t\tthis.safeAreaInsets = safeAreaInsets.bottom\n\t\t\t\t\t// #endif\n\t\t\t\t} else {\n\t\t\t\t\tthis.safeAreaInsets = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\tfixSize()\n\t\t\t// #ifdef H5\n\t\t\t// window.addEventListener('resize', fixSize)\n\t\t\t// this.$once('hook:beforeDestroy', () => {\n\t\t\t// \twindow.removeEventListener('resize', fixSize)\n\t\t\t// })\n\t\t\t// #endif\n\t\t},\n\t\t// #ifndef VUE3\n\t\t// TODO vue2\n\t\tdestroyed() {\n\t\t\tthis.setH5Visible()\n\t\t},\n\t\t// #endif\n\t\t// #ifdef VUE3\n\t\t// TODO vue3\n\t\tunmounted() {\n\t\t\tthis.setH5Visible()\n\t\t},\n\t\t// #endif\n\t\tcreated() {\n\t\t\t// this.mkclick =  this.isMaskClick || this.maskClick\n\t\t\tif (this.isMaskClick === null && this.maskClick === null) {\n\t\t\t\tthis.mkclick = true\n\t\t\t} else {\n\t\t\t\tthis.mkclick = this.isMaskClick !== null ? this.isMaskClick : this.maskClick\n\t\t\t}\n\t\t\tif (this.animation) {\n\t\t\t\tthis.duration = 300\n\t\t\t} else {\n\t\t\t\tthis.duration = 0\n\t\t\t}\n\t\t\t// TODO 处理 message 组件生命周期异常的问题\n\t\t\tthis.messageChild = null\n\t\t\t// TODO 解决头条冒泡的问题\n\t\t\tthis.clearPropagation = false\n\t\t\tthis.maskClass.backgroundColor = this.maskBackgroundColor\n\t\t},\n\t\tmethods: {\n\t\t\tsetH5Visible() {\n\t\t\t\t// #ifdef H5\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = 'visible'\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t/**\n\t\t\t * 公用方法，不显示遮罩层\n\t\t\t */\n\t\t\tcloseMask() {\n\t\t\t\tthis.maskShow = false\n\t\t\t},\n\t\t\t/**\n\t\t\t * 公用方法，遮罩层禁止点击\n\t\t\t */\n\t\t\tdisableMask() {\n\t\t\t\tthis.mkclick = false\n\t\t\t},\n\t\t\t// TODO nvue 取消冒泡\n\t\t\tclear(e) {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\te.stopPropagation()\n\t\t\t\t// #endif\n\t\t\t\tthis.clearPropagation = true\n\t\t\t},\n\n\t\t\topen(direction) {\n\t\t\t\t// fix by mehaotian 处理快速打开关闭的情况\n\t\t\t\tif (this.showPopup) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet innerType = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\n\t\t\t\tif (!(direction && innerType.indexOf(direction) !== -1)) {\n\t\t\t\t\tdirection = this.type\n\t\t\t\t}\n\t\t\t\tif (!this.config[direction]) {\n\t\t\t\t\tconsole.error('缺少类型：', direction)\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis[this.config[direction]]()\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\tshow: true,\n\t\t\t\t\ttype: direction\n\t\t\t\t})\n\t\t\t},\n\t\t\tclose(type) {\n\t\t\t\tthis.showTrans = false\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\tshow: false,\n\t\t\t\t\ttype: this.type\n\t\t\t\t})\n\t\t\t\tclearTimeout(this.timer)\n\t\t\t\t// // 自定义关闭事件\n\t\t\t\t// this.customOpen && this.customClose()\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis.showPopup = false\n\t\t\t\t}, 300)\n\t\t\t},\n\t\t\t// TODO 处理冒泡事件，头条的冒泡事件有问题 ，先这样兼容\n\t\t\ttouchstart() {\n\t\t\t\tthis.clearPropagation = false\n\t\t\t},\n\n\t\t\tonTap() {\n\t\t\t\tif (this.clearPropagation) {\n\t\t\t\t\t// fix by mehaotian 兼容 nvue\n\t\t\t\t\tthis.clearPropagation = false\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.$emit('maskClick')\n\t\t\t\tif (!this.mkclick) return\n\t\t\t\tthis.close()\n\t\t\t},\n\t\t\t/**\n\t\t\t * 顶部弹出样式处理\n\t\t\t */\n\t\t\ttop(type) {\n\t\t\t\tthis.popupstyle = this.isDesktop ? 'fixforpc-top' : 'top'\n\t\t\t\tthis.ani = ['slide-top']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tbackgroundColor: this.bg\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.showTrans = true\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tif (this.messageChild && this.type === 'message') {\n\t\t\t\t\t\tthis.messageChild.timerClose()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 底部弹出样式处理\n\t\t\t */\n\t\t\tbottom(type) {\n\t\t\t\tthis.popupstyle = 'bottom'\n\t\t\t\tthis.ani = ['slide-bottom']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tpaddingBottom: this.safeAreaInsets + 'px',\n\t\t\t\t\tbackgroundColor: this.bg\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.showTrans = true\n\t\t\t},\n\t\t\t/**\n\t\t\t * 中间弹出样式处理\n\t\t\t */\n\t\t\tcenter(type) {\n\t\t\t\tthis.popupstyle = 'center'\n\t\t\t\tthis.ani = ['zoom-out', 'fade']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column',\n\t\t\t\t\t/* #endif */\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tjustifyContent: 'center',\n\t\t\t\t\talignItems: 'center'\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.showTrans = true\n\t\t\t},\n\t\t\tleft(type) {\n\t\t\t\tthis.popupstyle = 'left'\n\t\t\t\tthis.ani = ['slide-left']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tbackgroundColor: this.bg,\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column'\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.showTrans = true\n\t\t\t},\n\t\t\tright(type) {\n\t\t\t\tthis.popupstyle = 'right'\n\t\t\t\tthis.ani = ['slide-right']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tbackgroundColor: this.bg,\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column'\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.showTrans = true\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style lang=\"scss\">\n\t.uni-popup {\n\t\tposition: fixed;\n\t\t/* #ifndef APP-NVUE */\n\t\tz-index: 99;\n\n\t\t/* #endif */\n\t\t&.top,\n\t\t&.left,\n\t\t&.right {\n\t\t\t/* #ifdef H5 */\n\t\t\ttop: var(--window-top);\n\t\t\t/* #endif */\n\t\t\t/* #ifndef H5 */\n\t\t\ttop: 0;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t.uni-popup__wrapper {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tdisplay: block;\n\t\t\t/* #endif */\n\t\t\tposition: relative;\n\n\t\t\t/* iphonex 等安全区设置，底部安全区适配 */\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\t// padding-bottom: constant(safe-area-inset-bottom);\n\t\t\t// padding-bottom: env(safe-area-inset-bottom);\n\t\t\t/* #endif */\n\t\t\t&.left,\n\t\t\t&.right {\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tpadding-top: var(--window-top);\n\t\t\t\t/* #endif */\n\t\t\t\t/* #ifndef H5 */\n\t\t\t\tpadding-top: 0;\n\t\t\t\t/* #endif */\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\t}\n\n\t.fixforpc-z-index {\n\t\t/* #ifndef APP-NVUE */\n\t\tz-index: 999;\n\t\t/* #endif */\n\t}\n\n\t.fixforpc-top {\n\t\ttop: 0;\n\t}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753419037747\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}