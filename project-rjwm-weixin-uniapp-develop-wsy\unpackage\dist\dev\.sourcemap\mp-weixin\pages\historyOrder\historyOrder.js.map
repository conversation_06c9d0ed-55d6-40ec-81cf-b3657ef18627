{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/historyOrder/historyOrder.vue?99bc", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/historyOrder/historyOrder.vue?d4eb", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/historyOrder/historyOrder.vue?110a", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/historyOrder/historyOrder.vue?5f2e", "uni-app:///pages/historyOrder/historyOrder.vue", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/historyOrder/historyOrder.vue?6afe", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/pages/historyOrder/historyOrder.vue?0041"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Empty", "data", "recentOrdersList", "pageInfo", "page", "pageSize", "total", "status", "payStatus", "loadingType", "showTitle", "scrollinto", "scrollH", "tabIndex", "tabBars", "urlMap", "fn", "key", "textTip", "showConfirm", "isEmpty", "onLoad", "onUnload", "onReady", "uni", "success", "onPullDownRefresh", "onReachBottom", "methods", "numes", "list", "count", "statusWord", "getOvertime", "getList", "params", "title", "mask", "setTimeout", "res", "oneMoreOrder", "pages", "routeIndex", "delta", "changeTab", "onChangeSwiperTab", "dataAdd", "lower", "goDetail", "url", "handleReminder", "closePopup", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAoyB,CAAgB,sxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACgFxzB;AAOA;AAEA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;QACA;UACAC;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAF;IACA;EACA;EACAG;IACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC,yCACA;IACAC;MACA;MACA;MACAC,mBACAA;QACAC;QACAzB;MACA;MACA;QAAAyB;QAAAzB;MAAA;IACA;IACA0B;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA7B;QACAD;MACA;MACA+B;MACAX;QAAAY;QAAAC;MAAA;MACArB;QACA;UACAsB;YACAd;UACA;UACA,yDACAe,iBACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC,6BACA;kBAAA;gBAAA,EACA,EACA;gBAAA;gBAAA,OACA;cAAA;gBACA;kBACA;oBACAlB;sBACAmB;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA,OACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAxB;QAAAyB;MAAA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA5B;QACAyB;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnSA;AAAA;AAAA;AAAA;AAA+/C,CAAgB,65CAAG,EAAC,C;;;;;;;;;;;ACAnhD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/historyOrder/historyOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/historyOrder/historyOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./historyOrder.vue?vue&type=template&id=5cf07246&scoped=true&\"\nvar renderjs\nimport script from \"./historyOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./historyOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./historyOrder.vue?vue&type=style&index=0&id=5cf07246&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5cf07246\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/historyOrder/historyOrder.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./historyOrder.vue?vue&type=template&id=5cf07246&scoped=true&\"", "var components\ntry {\n  components = {\n    uniNavBar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-bar/uni-nav-bar\" */ \"@/components/uni-nav-bar/uni-nav-bar.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recentOrdersList && _vm.recentOrdersList.length > 0\n  var l1 = _vm.__map(_vm.tabBars, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 = g0\n      ? _vm.__map(_vm.recentOrdersList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = Number(index)\n          var g1 = _vm.recentOrdersList.length\n          var m1 = _vm.statusWord(item.status)\n          var g2 = item.amount.toFixed(2)\n          var m2 = _vm.numes(item.orderDetailList)\n          var m3 = item.status === 1 && _vm.getOvertime(item.orderTime) > 0\n          return {\n            $orig: $orig,\n            m0: m0,\n            g1: g1,\n            m1: m1,\n            g2: g2,\n            m2: m2,\n            m3: m3,\n          }\n        })\n      : null\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./historyOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./historyOrder.vue?vue&type=script&lang=js&\"", "<!--历史订单-->\n<template>\n  <view class=\"history_order\">\n    <uni-nav-bar @clickLeft=\"goBack\" left-icon=\"back\" leftIcon=\"arrowleft\" title=\"历史订单\" statusBar=\"true\" fixed=\"true\"\n      color=\"#ffffff\" backgroundColor=\"#333333\"></uni-nav-bar>\n    <!-- 根据scrollinto和:id=\"'tab'+index\"切换下方轮播 -->\n    <scroll-view scroll-x class=\"scroll-row\" :scroll-into-view=\"scrollinto\" :scroll-with-animation=\"true\" enable-flex>\n      <view v-for=\"(item, index) in tabBars\" :key=\"index\" :id=\"'tab' + index\" class=\"scroll-row-item\"\n        @click=\"changeTab(index)\">\n        <view :class=\"tabIndex == index ? 'scroll-row-item-act' : ''\"><text class=\"line\"></text>{{ item }}</view>\n      </view>\n    </scroll-view>\n    <!--  滑块内容 对应的是顶部选项卡的切换 :current=\"tabIndex\"  设置的是y方向上可以滚动-->\n    <swiper :current=\"tabIndex\" @change=\"onChangeSwiperTab\" :style=\"{ height: scrollH + 'px' }\">\n      <swiper-item v-for=\"(item, index) in tabBars\" :key=\"index\">\n        <!-- 垂直滚动区域  scroll和swiper的高度都要给且是一样的高度-->\n        <scroll-view scroll-y=\"true\" :style=\"{ height: scrollH + 'px' }\" @scrolltolower=\"lower\">\n          <!-- 可垂直滚动区域 显示真正内容-->\n          <view class=\"main recent_orders\" v-if=\"recentOrdersList && recentOrdersList.length > 0\">\n            <!-- 历史订单列表 -->\n            <view class=\"box order_lists\" v-for=\"(item, index) in recentOrdersList\" :key=\"index\" :class=\"{\n              'item-last': Number(index) + 1 === recentOrdersList.length,\n            }\">\n              <!-- 时间和支付状态 -->\n              <view class=\"date_type\">\n                <!-- 时间 -->\n                <text class=\"time\">{{ item.orderTime }}</text>\n                <!-- 支付状态 -->\n                <text class=\"type status\" :class=\"{ status: item.status == 2 }\">{{\n                  statusWord(item.status)\n                }}</text>\n              </view>\n              <!-- 点菜的内容 -->\n              <view class=\"orderBox\" @click=\"goDetail(item.id)\">\n                <view class=\"food_num\">\n                  <scroll-view scroll-x=\"true\" class=\"pic\" style=\"width: 100%; overflow: hidden; white-space: nowrap\">\n                    <view class=\"food_num_item\" v-for=\"(num, y) in item.orderDetailList\" :key=\"y\">\n                      <view class=\"img\">\n                        <image :src=\"num.image\"></image>\n                      </view>\n                      <view class=\"food\">{{ num.name }}</view>\n                    </view>\n                  </scroll-view>\n                </view>\n                <!-- 商品数量及金额 -->\n                <view class=\"numAndAum\">\n                  <view><text>￥{{ item.amount.toFixed(2) }}</text></view>\n                  <view><text>共{{ numes(item.orderDetailList).count }}件</text></view>\n                </view>\n              </view>\n              <view class=\"againBtn\">\n                <button class=\"new_btn\" type=\"default\" @click=\"oneMoreOrder(item.id)\">\n                  再来一单\n                </button>\n                <button class=\"new_btn btn\" type=\"default\" @click=\"goDetail(item.id)\"\n                  v-if=\"item.status === 1 && getOvertime(item.orderTime) > 0\">\n                  去支付\n                </button>\n                <button class=\"new_btn btn\" type=\"default\" @click=\"handleReminder('center', item.id)\"\n                  v-if=\"item.status === 2\">\n                  催单\n                </button>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n      </swiper-item>\n    </swiper>\n    <uni-popup ref=\"commonPopup\" class=\"comPopupBox\">\n      <view class=\"popup-content\">\n        <view class=\"text\">{{ textTip }}</view>\n        <view class=\"btn\" v-if=\"showConfirm\">\n          <view @click=\"closePopup\">确认</view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport {\n  getOrderPage,\n  repetitionOrder,\n  reminderOrder,\n  delShoppingCart,\n  queryOrdersCheckStatus\n} from \"../api/api.js\";\nimport { mapMutations } from \"vuex\";\nimport Empty from \"@/components/empty/empty\";\nimport { statusWord, getOvertime } from \"@/utils/index.js\";\nexport default {\n  components: {\n    Empty,\n  },\n  data() {\n    return {\n      recentOrdersList: [],\n      pageInfo: {\n        page: 1,\n        pageSize: 10,\n        total: 0,\n      },\n      status: \"\",\n      payStatus: \"\",\n      loadingType: 0,\n      showTitle: false,\n      scrollinto: \"tab0\",\n      scrollH: 0,\n      tabIndex: 0,\n      // tabBars: [\"全部订单\", \"待付款\", \"退款\", \"已完成\", \"派送中\"],\n      tabBars: [\"全部订单\", \"待付款\", \"退款\"],\n      // 状态对应的接口和参数\n      urlMap: {\n        0: {\n          fn: getOrderPage,\n          key: \"status\",\n        },\n        1: {\n          fn: getOrderPage,\n          key: \"status\",\n        },\n        2: {\n          fn: queryOrdersCheckStatus,\n          key: \"payStatus\",\n        },\n      },\n      textTip: \"\",\n      showConfirm: false,\n      isEmpty: false,\n    };\n  },\n  onLoad() {\n    this.getList();\n  },\n  onUnload() {\n    this.showTitle = false;\n  },\n  onReady() {\n    uni.getSystemInfo({\n      success: (res) => {\n        this.scrollH = res.windowHeight - uni.upx2px(100);\n      },\n    });\n  },\n  onPullDownRefresh() {\n    this.pageInfo.page = 1;\n    this.loadingType = 0;\n    this.recentOrdersList = [];\n    this.finished = false;\n    this.getList();\n    uni.stopPullDownRefresh();\n    this.showTitle = true;\n  },\n  onReachBottom() {\n    if (this.recentOrdersList.length < Number(this.pageInfo.total)) {\n      this.pageInfo.page++;\n      this.loadingStatus = \"loading\";\n      this.getList(this.status);\n      this.showTitle = true;\n    }\n  },\n  methods: {\n    ...mapMutations([\"setAddressBackUrl\"]),\n    numes(list) {\n      let count = 0;\n      let total = 0;\n      list.length > 0 &&\n        list.forEach((obj) => {\n          count += Number(obj.number);\n          total += Number(obj.number) * Number(obj.amount);\n        });\n      return { count: count, total: total };\n    },\n    statusWord(status) {\n      return statusWord(status);\n    },\n    getOvertime(time) {\n      return getOvertime(time);\n    },\n    // 获取历史订单列表\n    getList() {\n      const key = this.urlMap[this.tabIndex].key;\n      const fn = this.urlMap[this.tabIndex].fn;\n      const params = {\n        pageSize: 10,\n        page: this.pageInfo.page,\n      };\n      params[key] = this[key]\n      uni.showLoading({ title: \"加载中\", mask: true });\n      fn(params).then((res) => {\n        if (res.code === 1) {\n          setTimeout(function () {\n            uni.hideLoading();\n          }, 100);\n          this.recentOrdersList = this.recentOrdersList.concat(\n            res.data.records\n          );\n          this.pageInfo.total = res.data.total;\n          this.isEmpty = true;\n        }\n      });\n    },\n    // 再来一单\n    async oneMoreOrder(id) {\n      let pages = getCurrentPages();\n      let routeIndex = pages.findIndex(\n        (item) => item.route === \"pages/index/index\"\n      );\n      // 先清空购物车\n      await delShoppingCart();\n      repetitionOrder(id).then((res) => {\n        if (res.code === 1) {\n          uni.navigateBack({\n            delta: routeIndex > -1 ? pages.length - routeIndex : 1,\n          });\n        }\n      });\n    },\n    // tab选项卡切换轮播\n    changeTab(index) {\n      // 点击的还是当前数据的时候直接return\n      if (this.tabIndex == index) {\n        return;\n      }\n      this.tabIndex = index;\n      if (index === 1) {\n        // 待付款\n        this.status = 1;\n        this.payStatus = 0\n      } else if (index === 2) {\n        // 退款\n        this.status = 6;\n        this.payStatus = 2\n      }\n      else {\n        // 全部\n        this.status = \"\";\n        this.payStatus = \"\"\n      }\n      this.pageInfo.page = 1;\n      this.recentOrdersList = [];\n      this.getList();\n      // 滑动\n      this.scrollinto = \"tab\" + index;\n    },\n    onChangeSwiperTab(e) {\n      this.changeTab(e.detail.current);\n    },\n    dataAdd() {\n      const pages = Math.ceil(this.pageInfo.total / 10); //计算总页数\n      if (this.pageInfo.page === pages) {\n        this.loadingText = \"没有更多了\";\n        this.loading = true;\n      } else {\n        this.pageInfo.page++;\n        this.getList();\n      }\n    },\n\n    lower() {\n      this.loadingText = \"数据加载中...\";\n      this.loading = true;\n      this.dataAdd();\n    },\n    // 去详情页面\n    goDetail(id) {\n      this.setAddressBackUrl(\"/pages/historyOrder/historyOrder\");\n      uni.navigateTo({ url: \"/pages/details/index?orderId=\" + id });\n    },\n    // 催单\n    handleReminder(type, id) {\n      reminderOrder(id).then((res) => {\n        if (res.code === 1) {\n          this.showConfirm = true;\n          this.textTip = \"您的催单信息已发出！\";\n          this.$refs.commonPopup.open(type);\n          this.getList(this.status);\n        }\n      });\n    },\n    // 关闭弹层\n    closePopup(type) {\n      this.$refs.commonPopup.close(type);\n    },\n    // 返回我的\n    goBack() {\n      uni.redirectTo({\n        url: \"/pages/my/my\",\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.history_order {\n  height: 100%;\n\n  .recent_orders {\n    padding-top: 8rpx;\n  }\n}\n\n.scroll-row {\n  height: 88rpx;\n  line-height: 88rpx;\n  background-color: #fff;\n  padding: 0 30rpx;\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);\n  width: 100vw;\n  box-sizing: border-box;\n  flex-wrap: nowrap;\n  overflow: auto;\n  display: flex;\n}\n\n.scroll-row-item {\n  margin-right: 88rpx;\n  color: #666;\n  display: inline-block;\n  font-size: 28rpx;\n  flex-shrink: 0;\n}\n\n.scroll-row-item-act {\n  color: #333;\n  position: relative;\n  font-weight: 600;\n\n  .line {\n    width: 32rpx;\n    height: 8rpx;\n    display: block;\n    background: #ffc200;\n    border-radius: 8rpx;\n    transform: translate(-50%, -50%);\n    position: absolute;\n    bottom: -4rpx;\n    left: 50%;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./historyOrder.vue?vue&type=style&index=0&id=5cf07246&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./historyOrder.vue?vue&type=style&index=0&id=5cf07246&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753418841462\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}