<view class="more_norm_pop data-v-ee81424a"><view class="title data-v-ee81424a">{{moreNormDishdata.name}}</view><scroll-view class="items_cont data-v-ee81424a" scroll-y="true" scroll-top="0rpx"><block wx:for="{{$root.l1}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="item_row data-v-ee81424a"><view class="flavor_name data-v-ee81424a">{{obj.$orig.name}}</view><view class="flavor_item data-v-ee81424a"><block wx:for="{{obj.l0}}" wx:for-item="item" wx:for-index="ind" wx:key="ind"><view data-event-opts="{{[['tap',[['checkMoreNormPop',['$0','$1'],[[['moreNormdata','',index,'value']],[['moreNormdata','',index],['value','',ind]]]]]]]}}" class="{{['data-v-ee81424a',(true)?'item':'',(item.g0!==-1)?'act':'']}}" bindtap="__e">{{''+item.$orig+''}}</view></block></view></view></block></scroll-view><view class="but_item data-v-ee81424a"><view class="price data-v-ee81424a"><text class="ico data-v-ee81424a">￥</text>{{''+moreNormDishdata.price+''}}</view><view class="active data-v-ee81424a"><view data-event-opts="{{[['tap',[['addShop',['$0','普通'],['moreNormDishdata']]]]]}}" class="dish_card_add data-v-ee81424a" bindtap="__e">加入购物车</view></view></view><view data-event-opts="{{[['tap',[['closeMoreNorm',['$0'],['moreNormDishdata']]]]]}}" class="close data-v-ee81424a" bindtap="__e"><image class="close_img data-v-ee81424a" src="../../../static/but_close.png" mode></image></view></view>