<template>
  <view class="products-list-container">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="nav-back" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="nav-title">商品列表</view>
    </view>
    
    <!-- 商品网格 -->
    <view class="products-grid">
      <view
        class="product-card"
        v-for="product in productList"
        :key="product.id"
        @click="goToProduct(product)"
      >
        <view class="product-image-wrapper">
          <image class="product-image" :src="product.image" mode="aspectFill"></image>
        </view>
        <view class="product-info">
          <view class="product-name">{{ product.name }}</view>
          <view class="product-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{ product.price.toFixed(2) }}</text>
          </view>
        </view>
        <view class="product-button">
          <button class="view-button" @click.stop="goToProduct(product)">查看详情</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import midManager from "../../utils/midManager.js"

export default {
  data() {
    return {
      productList: [
        { id: 1, name: '精选美味商品1', price: 29.90, image: '/static/logo_ruiji.png', path: '/pages/products/product1/index' },
        { id: 2, name: '精选美味商品2', price: 35.90, image: '/static/logo_ruiji.png', path: '/pages/products/product2/index' },
        { id: 3, name: '精选美味商品3', price: 42.90, image: '/static/logo_ruiji.png', path: '/pages/products/product3/index' },
        { id: 4, name: '精选美味商品4', price: 38.90, image: '/static/logo_ruiji.png', path: '/pages/products/product4/index' },
        { id: 5, name: '精选美味商品5', price: 45.90, image: '/static/logo_ruiji.png', path: '/pages/products/product5/index' },
        { id: 6, name: '精选美味商品6', price: 32.90, image: '/static/logo_ruiji.png', path: '/pages/products/product6/index' },
        { id: 7, name: '精选美味商品7', price: 48.90, image: '/static/logo_ruiji.png', path: '/pages/products/product7/index' },
        { id: 8, name: '精选美味商品8', price: 36.90, image: '/static/logo_ruiji.png', path: '/pages/products/product8/index' },
        { id: 9, name: '精选美味商品9', price: 52.90, image: '/static/logo_ruiji.png', path: '/pages/products/product9/index' },
        { id: 10, name: '精选美味商品10', price: 58.90, image: '/static/logo_ruiji.png', path: '/pages/products/product10/index' }
      ]
    }
  },

  onLoad(options) {
    // 如果URL中有mid参数，保存它
    if (options && options.mid) {
      midManager.setMid(options.mid)
    }
  },

  methods: {
    goBack() {
      uni.navigateBack({ delta: 1 })
    },

    goToProduct(product) {
      // 使用midManager自动添加保存的mid参数
      midManager.navigateTo(product.path)
    }
  }
}
</script>

<style lang="scss" scoped>
.products-list-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  
  .nav-back {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      font-size: 40rpx;
      color: #333;
      font-weight: bold;
    }
  }
  
  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-right: 60rpx;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 30rpx;
}

.product-card {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.product-image-wrapper {
  width: 100%;
  height: 200rpx;
  overflow: hidden;
  
  .product-image {
    width: 100%;
    height: 100%;
  }
}

.product-info {
  padding: 20rpx;
  
  .product-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .product-price {
    display: flex;
    align-items: baseline;
    
    .price-symbol {
      font-size: 24rpx;
      color: #ff6b35;
      font-weight: bold;
    }
    
    .price-value {
      font-size: 32rpx;
      color: #ff6b35;
      font-weight: bold;
      margin-left: 5rpx;
    }
  }
}

.product-button {
  padding: 0 20rpx 20rpx;
  
  .view-button {
    width: 100%;
    height: 60rpx;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: #fff;
    font-size: 24rpx;
    border-radius: 30rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
