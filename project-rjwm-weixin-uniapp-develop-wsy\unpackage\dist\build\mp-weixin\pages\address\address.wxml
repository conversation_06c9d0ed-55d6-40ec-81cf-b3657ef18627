<view class="customer-box data-v-a1b801d6"><uni-nav-bar vue-id="91826bd4-1" left-icon="back" leftIcon="arrowleft" title="地址管理" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-a1b801d6" bind:__l="__l"></uni-nav-bar><view class="address data-v-a1b801d6" style="{{'height:'+('calc(100% - 136rpx - '+statusBarHeight+' - 44px - 20rpx)')+';'}}"><block wx:if="{{$root.g0}}"><view class="address_content data-v-a1b801d6"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="address_liests data-v-a1b801d6"><view data-event-opts="{{[['tap',[['choseAddress',[index,'$0'],[[['addressList','',index]]]]]]]}}" class="list_item_top data-v-a1b801d6" catchtap="__e"><view class="item_left data-v-a1b801d6"><view class="details data-v-a1b801d6"><text class="{{['tag','data-v-a1b801d6','tag'+item.$orig.label]}}">{{item.m0}}</text><text class="address_word data-v-a1b801d6">{{item.$orig.provinceName+item.$orig.cityName+item.$orig.districtName+item.$orig.detail}}</text></view><view class="sale data-v-a1b801d6"><text class="name data-v-a1b801d6">{{item.$orig.sex==="0"?item.$orig.consignee+" 先生":item.$orig.consignee+" 女士"}}</text><text class="num data-v-a1b801d6">{{item.$orig.phone}}</text></view></view><view class="item_right data-v-a1b801d6"><image class="edit data-v-a1b801d6" src="../../static/edit.png" data-event-opts="{{[['tap',[['addOrEdit',['编辑','$0'],[[['addressList','',index]]]]]]]}}" catchtap="__e"></image></view></view><view class="list_item_bottom data-v-a1b801d6"><label data-event-opts="{{[['tap',[['getRadio',[index,'$0'],[[['addressList','',index]]]]]]]}}" class="radio data-v-a1b801d6" catchtap="__e"><block wx:if="{{testValue}}"><radio class="item_radio data-v-a1b801d6" color="#FFC200" value="{{item.$orig.id}}" checked="{{item.$orig.isDefault===1&&isActive===index}}" data-event-opts="{{[['tap',[['getRadio',[index,'$0'],[[['addressList','',index]]]]]]]}}" catchtap="__e"></radio></block>设为默认地址</label></view></view></block></view></block><block wx:if="{{isEmpty}}"><empty vue-id="91826bd4-2" boxHeight="100%" textLabel="一个地址都没有哦" class="data-v-a1b801d6" bind:__l="__l"></empty></block><view class="add_address data-v-a1b801d6"><button class="add_btn data-v-a1b801d6" type="primary" plain="true" data-event-opts="{{[['tap',[['addOrEdit',['新增']]]]]}}" bindtap="__e"><text class="add-icon data-v-a1b801d6">+</text>新增收货地址</button></view></view></view>