{"version": 3, "sources": ["webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?fb64", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?4cfb", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?ef30", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?4de4", "uni-app:///uni_modules/uni-badge/components/uni-badge/uni-badge.vue", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?f5cd", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-badge/components/uni-badge/uni-badge.vue?94ee"], "names": ["name", "emits", "props", "type", "default", "inverted", "isDot", "maxNum", "absolute", "offset", "text", "size", "customStyle", "data", "computed", "width", "classNames", "positionStyle", "h", "w", "rightTop", "right", "top", "rightBottom", "bottom", "leftBottom", "left", "leftTop", "dotStyle", "min<PERSON><PERSON><PERSON>", "height", "padding", "borderRadius", "displayValue", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAiyB,CAAgB,mxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;ACSrzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,gBAyBA;EACAA;EACAC;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;EACA;EACAS;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA,IACAX,WAIA,KAJAA;QACAF,OAGA,KAHAA;QACAQ,OAEA,KAFAA;QACAH,WACA,KADAA;MAEA,QACAH,oDACA,sBACA,sBACAG,sCACA;IACA;IACAS;MACA;MACA;QACAC;MACA;QACAC;QACAD;MACA;MACA;MACA;MAEA;QACAE;UACAC;UACAC;QACA;QACAC;UACAF;UACAG;QACA;QACAC;UACAC;UACAF;QACA;QACAG;UACAD;UACAJ;QACA;MACA;MACA;MACA;IACA;IACAM;MACA;MACA;QACAb;QACAc;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA,IACA3B,QAGA,KAHAA;QACAI,OAEA,KAFAA;QACAH,SACA,KADAA;MAEA;IACA;EACA;EACA2B;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAAo+C,CAAgB,k4CAAG,EAAC,C;;;;;;;;;;;ACAx/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-badge/components/uni-badge/uni-badge.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-badge.vue?vue&type=template&id=7c66581c&\"\nvar renderjs\nimport script from \"./uni-badge.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-badge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-badge.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-badge/components/uni-badge/uni-badge.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=template&id=7c66581c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.text\n    ? _vm.__get_style([_vm.positionStyle, _vm.customStyle, _vm.dotStyle])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"uni-badge--x\">\n\t\t<slot />\n\t\t<text v-if=\"text\" :class=\"classNames\" :style=\"[positionStyle, customStyle, dotStyle]\"\n\t\t\tclass=\"uni-badge\" @click=\"onClick()\">{{displayValue}}</text>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * Badge 数字角标\n\t * @description 数字角标一般和其它控件（列表、9宫格等）配合使用，用于进行数量提示，默认为实心灰色背景\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=21\n\t * @property {String} text 角标内容\n\t * @property {String} size = [normal|small] 角标内容\n\t * @property {String} type = [info|primary|success|warning|error] 颜色类型\n\t * \t@value info 灰色\n\t * \t@value primary 蓝色\n\t * \t@value success 绿色\n\t * \t@value warning 黄色\n\t * \t@value error 红色\n\t * @property {String} inverted = [true|false] 是否无需背景颜色\n\t * @property {Number} maxNum 展示封顶的数字值，超过 99 显示 99+\n\t * @property {String} absolute = [rightTop|rightBottom|leftBottom|leftTop] 开启绝对定位, 角标将定位到其包裹的标签的四角上\n\t * \t@value rightTop 右上\n\t * \t@value rightBottom 右下\n\t * \t@value leftTop 左上\n\t * \t@value leftBottom 左下\n\t * @property {Array[number]} offset\t距定位角中心点的偏移量，只有存在 absolute 属性时有效，例如：[-10, -10] 表示向外偏移 10px，[10, 10] 表示向 absolute 指定的内偏移 10px\n\t * @property {String} isDot = [true|false] 是否显示为一个小点\n\t * @event {Function} click 点击 Badge 触发事件\n\t * @example <uni-badge text=\"1\"></uni-badge>\n\t */\n\n\texport default {\n\t\tname: 'UniBadge',\n\t\temits: ['click'],\n\t\tprops: {\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'error'\n\t\t\t},\n\t\t\tinverted: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tisDot: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tmaxNum: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 99\n\t\t\t},\n\t\t\tabsolute: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\toffset: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn [0, 0]\n\t\t\t\t}\n\t\t\t},\n\t\t\ttext: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tsize: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'small'\n\t\t\t},\n\t\t\tcustomStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {};\n\t\t},\n\t\tcomputed: {\n\t\t\twidth() {\n\t\t\t\treturn String(this.text).length * 8 + 12\n\t\t\t},\n\t\t\tclassNames() {\n\t\t\t\tconst {\n\t\t\t\t\tinverted,\n\t\t\t\t\ttype,\n\t\t\t\t\tsize,\n\t\t\t\t\tabsolute\n\t\t\t\t} = this\n\t\t\t\treturn [\n\t\t\t\t\tinverted ? 'uni-badge--' + type + '-inverted' : '',\n\t\t\t\t\t'uni-badge--' + type,\n\t\t\t\t\t'uni-badge--' + size,\n\t\t\t\t\tabsolute ? 'uni-badge--absolute' : ''\n\t\t\t\t].join(' ')\n\t\t\t},\n\t\t\tpositionStyle() {\n\t\t\t\tif (!this.absolute) return {}\n\t\t\t\tlet w = this.width / 2,\n\t\t\t\t\th = 10\n\t\t\t\tif (this.isDot) {\n\t\t\t\t\tw = 5\n\t\t\t\t\th = 5\n\t\t\t\t}\n\t\t\t\tconst x = `${- w  + this.offset[0]}px`\n\t\t\t\tconst y = `${- h + this.offset[1]}px`\n\n\t\t\t\tconst whiteList = {\n\t\t\t\t\trightTop: {\n\t\t\t\t\t\tright: x,\n\t\t\t\t\t\ttop: y\n\t\t\t\t\t},\n\t\t\t\t\trightBottom: {\n\t\t\t\t\t\tright: x,\n\t\t\t\t\t\tbottom: y\n\t\t\t\t\t},\n\t\t\t\t\tleftBottom: {\n\t\t\t\t\t\tleft: x,\n\t\t\t\t\t\tbottom: y\n\t\t\t\t\t},\n\t\t\t\t\tleftTop: {\n\t\t\t\t\t\tleft: x,\n\t\t\t\t\t\ttop: y\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst match = whiteList[this.absolute]\n\t\t\t\treturn match ? match : whiteList['rightTop']\n\t\t\t},\n\t\t\tdotStyle() {\n\t\t\t\tif (!this.isDot) return {}\n\t\t\t\treturn {\n\t\t\t\t\twidth: '10px',\n\t\t\t\t\tminWidth: '0',\n\t\t\t\t\theight: '10px',\n\t\t\t\t\tpadding: '0',\n\t\t\t\t\tborderRadius: '10px'\n\t\t\t\t}\n\t\t\t},\n\t\t\tdisplayValue() {\n\t\t\t\tconst {\n\t\t\t\t\tisDot,\n\t\t\t\t\ttext,\n\t\t\t\t\tmaxNum\n\t\t\t\t} = this\n\t\t\t\treturn isDot ? '' : (Number(text) > maxNum ? `${maxNum}+` : text)\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tonClick() {\n\t\t\t\tthis.$emit('click');\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" >\n\t$uni-primary: #2979ff !default;\n\t$uni-success: #4cd964 !default;\n\t$uni-warning: #f0ad4e !default;\n\t$uni-error: #dd524d !default;\n\t$uni-info: #909399 !default;\n\n\n\t$bage-size: 12px;\n\t$bage-small: scale(0.8);\n\n\t.uni-badge--x {\n\t\t/* #ifdef APP-NVUE */\n\t\t// align-self: flex-start;\n\t\t/* #endif */\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: inline-block;\n\t\t/* #endif */\n\t\tposition: relative;\n\t}\n\n\t.uni-badge--absolute {\n\t\tposition: absolute;\n\t}\n\n\t.uni-badge--small {\n\t\ttransform: $bage-small;\n\t\ttransform-origin: center center;\n\t}\n\n\t.uni-badge {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\toverflow: hidden;\n\t\tbox-sizing: border-box;\n\t\tfont-feature-settings: \"tnum\";\n\t\tmin-width: 20px;\n\t\t/* #endif */\n\t\tjustify-content: center;\n\t\tflex-direction: row;\n\t\theight: 20px;\n\t\tpadding: 0 4px;\n\t\tline-height: 18px;\n\t\tcolor: #fff;\n\t\tborder-radius: 100px;\n\t\tbackground-color: $uni-info;\n\t\tbackground-color: transparent;\n\t\tborder: 1px solid #fff;\n\t\ttext-align: center;\n\t\tfont-family: 'Helvetica Neue', Helvetica, sans-serif;\n\t\tfont-size: $bage-size;\n\t\t/* #ifdef H5 */\n\t\tz-index: 999;\n\t\tcursor: pointer;\n\t\t/* #endif */\n\n\t\t&--info {\n\t\t\tcolor: #fff;\n\t\t\tbackground-color: $uni-info;\n\t\t}\n\n\t\t&--primary {\n\t\t\tbackground-color: $uni-primary;\n\t\t}\n\n\t\t&--success {\n\t\t\tbackground-color: $uni-success;\n\t\t}\n\n\t\t&--warning {\n\t\t\tbackground-color: $uni-warning;\n\t\t}\n\n\t\t&--error {\n\t\t\tbackground-color: $uni-error;\n\t\t}\n\n\t\t&--inverted {\n\t\t\tpadding: 0 5px 0 0;\n\t\t\tcolor: $uni-info;\n\t\t}\n\n\t\t&--info-inverted {\n\t\t\tcolor: $uni-info;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t&--primary-inverted {\n\t\t\tcolor: $uni-primary;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t&--success-inverted {\n\t\t\tcolor: $uni-success;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t&--warning-inverted {\n\t\t\tcolor: $uni-warning;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t&--error-inverted {\n\t\t\tcolor: $uni-error;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-badge.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753419037773\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}