(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/remark/index"],{"7de7":function(e,t,r){"use strict";(function(e){var n=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r("7ca3")),i=r("8f59");function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var c={data:function(){return{remark:"",numVal:0}},computed:{getVal:function(){var e=this.validateTextLength(this.remark);e<=50?this.numVal=Math.floor(e):this.remark=this.remark.substring(0,50)}},onLoad:function(){console.log(this.remarkData()),""===this.getRemark?this.remark=this.remark:(this.remark=this.remarkData(),this.numVal=this.remark.length)},methods:o(o(o({},(0,i.mapMutations)(["setRemark"])),(0,i.mapState)(["remarkData"])),{},{goBack:function(){e.redirectTo({url:"/pages/order/index"})},handleSaveRemark:function(){e.redirectTo({url:"/pages/order/index"}),this.setRemark(this.remark)},validateTextLength:function(e){var t,r=e.match(/([\u4e00-\u9fa5]|[\u3000-\u303F]|[\uFF00-\uFF60])/g);return r?(t=r.length+.5*(e.length-r.length),t):.5*e.length}})};t.default=c}).call(this,r("df3c")["default"])},bbd1:function(e,t,r){"use strict";r.r(t);var n=r("7de7"),a=r.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);t["default"]=a.a},be6c:function(e,t,r){"use strict";r.r(t);var n=r("fa79"),a=r("bbd1");for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);r("9170"),r("a666");var u=r("828b"),o=Object(u["a"])(a["default"],n["b"],n["c"],!1,null,"745a5bfa",null,!1,n["a"],void 0);t["default"]=o.exports},de64:function(e,t,r){"use strict";(function(e,t){var n=r("47a9");r("9579");n(r("3240"));var a=n(r("be6c"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(a.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},fa79:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return n}));var n={uniNavBar:function(){return r.e("components/uni-nav-bar/uni-nav-bar").then(r.bind(null,"42e0"))}},a=function(){var e=this.$createElement;this._self._c},i=[]}},[["de64","common/runtime","common/vendor"]]]);